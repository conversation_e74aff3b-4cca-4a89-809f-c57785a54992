<?php

use Entities\Service;
use MigrationModule\Helpers\NodeMigrationHelper;
use Models\Products\Product;
use Phinx\Migration\AbstractMigration;

class UpdateServiceTypeOfMailboxProducts extends AbstractMigration
{
    public function up()
    {
        $helper = new NodeMigrationHelper($this);

        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_INITIAL), 'serviceTypeId', Service::TYPE_MAILBOX_STANDARD);
        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_RENEWAL), 'serviceTypeId', Service::TYPE_MAILBOX_STANDARD);
        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL), 'serviceTypeId', Service::TYPE_MAILBOX_PREMIUM);
        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_PREMIUM_RENEWAL), 'serviceTypeId', Service::TYPE_MAILBOX_PREMIUM);
    }

    public function down()
    {
        $helper = new NodeMigrationHelper($this);

        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_INITIAL), 'serviceTypeId', Service::TYPE_REGISTERED_OFFICE);
        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_STANDARD_RENEWAL), 'serviceTypeId', Service::TYPE_REGISTERED_OFFICE);
        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL), 'serviceTypeId', Service::TYPE_REGISTERED_OFFICE);
        $helper->updateProperty($helper->getExistingNodeId(Product::PRODUCT_MAILBOX_PREMIUM_RENEWAL), 'serviceTypeId', Service::TYPE_REGISTERED_OFFICE);
    }
}