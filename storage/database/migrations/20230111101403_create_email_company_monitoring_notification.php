<?php

use Phinx\Migration\AbstractMigration;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use CompanyMonitoringModule\Emailers\CompanyMonitoringEmailer;

class CreateEmailCompanyMonitoringNotification extends AbstractMigration
{
    public function up()
    {
        if ($this->getNodeId() !== null) {
            return;
        }

        $page = new Page('Company Monitoring Notification Email');
        $page->setText("<div><p>Hello,</p><p>We have detected changes on your company.</p><p>MadeSimple.<p/></div>");

        $properties = [
            new Property('from', '<EMAIL>'),
            new Property('fromName', 'Companies Made Simple'),
            new Property('subject', 'We have detected changes on your tracked company'),
            new Property('tag1', 'company monitoring'),
            new Property('tag2', 'company monitoring'),
            new Property('tag3', 'company tracking'),
            new Property('tag4', 'company tracker'),
            new Property('templateName', CompanyMonitoringEmailer::COMPANY_MONITORING_EMAIL)
        ];

        $node = new Node(
            $page,
            $properties,
            CompanyMonitoringEmailer::COMPANY_MONITORING_EMAIL,
            137,
            null,
            'EmailAdminControler',
            40
        );

        $helper = new NodeMigrationHelper($this);
        $helper->create($node);
    }

    public function down()
    {
        $nodeId = $this->getNodeId();
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getNodeId(): ?string
    {
        $node = $this->fetchRow(sprintf("SELECT `node_id` FROM `%s` WHERE `name`='%s'", TBL_NODES, CompanyMonitoringEmailer::COMPANY_MONITORING_EMAIL));
        return $node['node_id'] ?? null;
    }
}
