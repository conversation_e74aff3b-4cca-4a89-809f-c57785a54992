<?php

declare(strict_types=1);

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class CreatePartnerPackagesParentFolder extends AbstractMigration
{
    public const FOLDER_NAME = 'partner_packages_folder';
    private const FOLDER_TITLE = 'Partner Packages';

    public function up(): void
    {
        $helper = new NodeMigrationHelper($this);

        if ($helper->getExistingNodeId(self::FOLDER_NAME)) {
            return;
        }

        $page = new Page(self::FOLDER_TITLE);

        $node = new Node(
            $page,
            [],
            self::FOLDER_NAME,
            108,
            null,
            'FolderAdminControler',
            16
        );

        $helper->create($node);
    }

    public function down(): void
    {
        $nodeId = (new NodeMigrationHelper($this))->getExistingNodeId(self::FOLDER_NAME);

        if (empty($nodeId)) {
            return;
        }

        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
    }
}
