<?php

namespace CMS\Proxy\__CG__\Entities\CompanyHouse\FormSubmission\CompanyIncorporation;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Document extends \Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Document implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }

    /**
     * {@inheritDoc}
     * @param string $name
     */
    public function & __get($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__get', [$name]);
        return parent::__get($name);
    }

    /**
     * {@inheritDoc}
     * @param string $name
     * @param mixed  $value
     */
    public function __set($name, $value)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__set', [$name, $value]);
        return parent::__set($name, $value);
    }

    /**
     * {@inheritDoc}
     * @param  string $name
     * @return boolean
     */
    public function __isset($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__isset', [$name]);

        return parent::__isset($name);
    }

    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'documentId', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'category', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'filename', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'filePath', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'custom', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'formSubmission'];
        }

        return ['__isInitialized__', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'documentId', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'category', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'filename', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'filePath', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'custom', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\CompanyIncorporation\\Document' . "\0" . 'formSubmission'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Document $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function setFormSubmission(\Entities\CompanyHouse\FormSubmission\CompanyIncorporation $formSubmission)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFormSubmission', [$formSubmission]);

        return parent::setFormSubmission($formSubmission);
    }

    /**
     * {@inheritDoc}
     */
    public function getDocumentId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getDocumentId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDocumentId', []);

        return parent::getDocumentId();
    }

    /**
     * {@inheritDoc}
     */
    public function getCategory()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCategory', []);

        return parent::getCategory();
    }

    /**
     * {@inheritDoc}
     */
    public function setCategory($category)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCategory', [$category]);

        return parent::setCategory($category);
    }

    /**
     * {@inheritDoc}
     */
    public function isMemorandumAndArticlesType(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMemorandumAndArticlesType', []);

        return parent::isMemorandumAndArticlesType();
    }

    /**
     * {@inheritDoc}
     */
    public function isSupportingDocumentType(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isSupportingDocumentType', []);

        return parent::isSupportingDocumentType();
    }

    /**
     * {@inheritDoc}
     */
    public function getFilename()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFilename', []);

        return parent::getFilename();
    }

    /**
     * {@inheritDoc}
     */
    public function setFilename($filename)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFilename', [$filename]);

        return parent::setFilename($filename);
    }

    /**
     * {@inheritDoc}
     */
    public function getFilePath()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFilePath', []);

        return parent::getFilePath();
    }

    /**
     * {@inheritDoc}
     */
    public function setFilePath($filePath)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFilePath', [$filePath]);

        return parent::setFilePath($filePath);
    }

    /**
     * {@inheritDoc}
     */
    public function getCustom()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustom', []);

        return parent::getCustom();
    }

    /**
     * {@inheritDoc}
     */
    public function setCustom($custom)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustom', [$custom]);

        return parent::setCustom($custom);
    }

    /**
     * {@inheritDoc}
     */
    public function __call($name, $args)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__call', [$name, $args]);

        return parent::__call($name, $args);
    }

    /**
     * {@inheritDoc}
     */
    public function __unset($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__unset', [$name]);

        return parent::__unset($name);
    }

}
