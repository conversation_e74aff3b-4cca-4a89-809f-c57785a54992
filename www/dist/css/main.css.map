{"version": 3, "sources": ["webpack:///./client/modules/idcheck/components/ValidationForm.vue?b2fb"], "names": [], "mappings": ";AAAA,oBAAoB,aAAa,8BAA8B,mBAAmB,wDAAwD,kBAAkB,aAAa,uBAAuB,8DAA8D,iBAAiB,mEAAmE,YAAY,8CAA8C,eAAe,oDAAoD,kBAAkB,2DAA2D,eAAe,kBAAkB,aAAa,iEAAiE,YAAY,gBAAgB,iBAAiB,gBAAgB,oCAAoC,aAAa,2BAA2B,kDAAkD,iBAAiB,kEAAkE,aAAa,uBAAuB,SAAS,mCAAmC", "file": "css/main.css", "sourcesContent": ["h1[data-v-28940fd4]{display:flex;justify-content:space-between;align-items:center}#validation-options .validation-option[data-v-28940fd4]{margin-bottom:1em;display:flex;align-items:flex-start}#validation-options .validation-option input[data-v-28940fd4]{margin-right:1em}#validation-options .validation-option label span[data-v-28940fd4]{opacity:0.5}#validation-options-triggers[data-v-28940fd4]{margin-top:3em}#validation-options-triggers label[data-v-28940fd4]{margin-bottom:1em}#validation-options-triggers #email-input[data-v-28940fd4]{margin-top:1em;margin-bottom:1em;display:flex}#validation-options-triggers #email-input input[data-v-28940fd4]{flex-grow:2;max-width:400px;margin-right:1em;text-align:left}.message-container[data-v-28940fd4]{display:flex;justify-content:flex-start}.message-container .message-icon[data-v-28940fd4]{margin-right:1em}.message-container .message-text .message-header[data-v-28940fd4]{display:flex;align-items:flex-start;margin:0}#status-container[data-v-28940fd4]{margin-top:3em}\n"], "sourceRoot": ""}