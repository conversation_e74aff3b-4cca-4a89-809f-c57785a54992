@charset "utf-8";
/* CSS Document */

/* =========================================  NORMALIZING  ================================================== */


/* Normalizes margin, padding */
body, div, dl, dt, dd, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, p, blockquote, th, td
{
	margin : 0;
	padding : 0;
}

/* Normalizes font-size for headers */
h1,h2,h3,h4,h5,h6 { font-size : 100%; }

/* Normalizes font-style and font-weight to normal */
address, caption, cite, code, dfn, th, var
{ font-style : normal; font-weight : normal; }

Removes list-style from lists
table { border-collapse : collapse; border-spacing : 0; }

/* Removes border from fieldset and img */
fieldset,img { border : 0; }

/* Left-aligns text in caption and th */
caption,th { text-align : left; }

p {
	padding-top:8px;
}
a {
	color:#006699;
	outline:none;
}
a:hover {
	text-decoration:none;
}
h1 {
	font-size:26px;
	color:#666666;
	font-weight:normal;
	margin-bottom:15px;
}
h2 {
	font-size:20px;
	color:#ff6600;
	margin:10px 0;
}
h3 {
	font-size:16px;
	color:#ff6600;
	margin:0 0 5px 0;
}
h4 {
	font-size:14px;
	font-weight:bold;
	margin-bottom:10px;
}
h5 {
	font-size:16px;
	color:#666666;
	margin-bottom:10px;
}
input {
	height:18px;
}
body {
	font-family:Arial, Helvetica, sans-serif;
	font-size:12px;
	background:#ffffff;
}
div#wrapper {
	width:980px;
	margin:0 auto;
}
/* ==================================== HEADER ============================================== */

div#header {
	height:114px;
	background:url(/front/imgs/top-banner2.png) top left no-repeat;
	color:#ffffff;
	font-size:13px;
	text-align:right;
	padding:15px 25px;
}
div#header a {
	color:#ffffff;
	text-decoration:none;
}
div#header a:hover {
	text-decoration:underline;
}
div#rss {
	height:24px;
	background:url(/front/imgs/rss.gif) 0px 4px no-repeat;
	padding:8px 0 0 32px;
}
div#headertext{
	font-size: 26px;
	padding: 36px 166px 0 0;
}
div#headertext h1{
	color: white;
}
.flogin {
	display:block;
	height:32px;
	float:right;
	padding:0;
	position:relative;
}
.flogin input {
	height:18px;
	width:120px;
	float:left;
	margin:6px 0 0 5px;

}
input.login {
	width:48px;
	height:16px;
	margin-top:8px;
}
.flogin a {
	display:block;
	margin:5px 0 0 5px;
	float:left;
	font-size:10px;
	line-height:11px;
	color:#000000;
	height:24px;
}
/* ==================================== MAIN MENU ============================================== */

ul#mainnav {
	display:block;
	list-style:none;
	padding:0;
	margin:0;
	height:37px;
}

ul#mainnav li {
	padding:0;
	margin:0;
	float:left;
}
ul#mainnav li a {
	display:block;
	height:37px;
	text-indent:-9999px;
}
ul#mainnav li a.btn_44 {
	background:url(/front/imgs/btn_home.gif) top left no-repeat;
	width:123px;
}
ul#mainnav li a.btn_home2 {
	background:url(/front/imgs/btn_home2.gif) top left no-repeat;
	width:123px;
}
ul#mainnav li a.btn_66 {
	background:url(/front/imgs/btn_why-choose-us.gif) top left no-repeat;
	width:187px;
}
ul#mainnav li a.btn_67 {
	background:url(/front/imgs/btn_how-it-works.gif) top left no-repeat;
	width:163px;
}
ul#mainnav li a.btn_68 {
	background:url(/front/imgs/btn_packages.gif) top left no-repeat;
	width:157px;
}
ul#mainnav li a.btn_236 {
	background:url(/front/imgs/btn_business-library.gif) top left no-repeat;
	width:184px;
}
ul#mainnav li a.btn_65 {
	background:url(/front/imgs/btn_my-account.gif) top left no-repeat;
	width:166px;
}
ul#mainnav li a.selected {
	background:url(/front/imgs/btn_my-account.gif) bottom left no-repeat;
}

ul#subnav {
	padding:0 40px 0 40px;
	margin:0;
	list-style:none;
	display:block;
	width:900px;
	height:23px;
	background:url(/front/imgs/bg_submenu.png) top left no-repeat;
	overflow:hidden;
}
ul#subnav li {
	float:left;
	padding:0;
	margin:0;
	margin-right:10px;
	display:block;
	background:url(/front/imgs/subnav_off_left.png) top left no-repeat;
	height:23px;
}
ul#subnav li a {
	display:block;
	float:left;
	height:18px;
	padding:5px 10px 0 10px;
	color:#000000;
	text-decoration:none;
	background:url(/front/imgs/subnav_off_right.png) top right no-repeat;
}
ul#subnav li.selected {
	background:url(/front/imgs/subnav_on_left.png) top left no-repeat;
}
ul#subnav li.selected a {
	background:url(/front/imgs/subnav_on_right.png) top right no-repeat;
}
/* ================================ MAIN CONTENT =========================================== */

div#content {
	padding:12px 0 0 0;
}
div#home-search {
	background:url(/front/imgs/home-search.png) top left no-repeat;
	width:541px;
	height:56px;
	padding:164px 0 0 40px;
	margin-bottom:10px;
}
div.search {
	background:url(/front/imgs/bg_search.png) top left no-repeat;
	width:316px;
	height:32px;
	padding:7px 0 0 13px;
	margin:10px 0;
}
input.searchbox2 {
	width:240px;
}
div#leftCol {
	width:187px;
	float:left;
}

div#maincontent1 {
	width:581px;
	float:left;
	margin-left:12px;
}
div#maincontent2 {
	width:780px;
	float:right;
}
div#maincontent4 {
	width:585px;
	float:left;
}
div#maincontent5 {
	width:781px;
	float:left;
}
.tab-orange,
.tab {
	background:url(/front/imgs/tab-left.png) top left no-repeat;
	height:31px;
	font-size:15px;
	font-weight:bold;
	float:left;
	display:inline;
	margin-left:10px;
}
.tab-orange {
	background:url(/front/imgs/tab-orange-left.png) top left no-repeat;
}
.tab-orange span,
.tab span {
	display:block;
	height:24px;
	background:url(/front/imgs/tab-right.png) top right no-repeat;
	padding:7px 20px 0 20px;
}
.tab-orange span {
	background:url(/front/imgs/tab-orange-right.png) top right no-repeat;
}
.services {
	height:90px;
	border-bottom:1px solid #cccccc;
	padding:12px 70px 0 17px;
}
.services .leftpart {
	width:590px;
	float:left;
}
.services img {
	border:1px solid #cccccc;
	margin-right:20px;
	float:left;
}
.services h3 {
	margin-bottom:0;
}
.services p {
	padding-top:0px;
}
.description {
	margin:15px 0;
	border-bottom:1px solid #cccccc;
	border-top:1px solid #cccccc;
	padding:7px 0 15px 0;
}
.description ul {
	padding:10px;
	margin:10px;
}
.description ul li {
	padding-top:10px;
}
.items {
	clear:left;
}
.item-head,
.item-desc {
	width:250px;
	float:left;
	height:92px;
	padding:0px 10px 0px 10px;
	border-bottom:1px solid #cccccc;
}
.item-head {
	height:45px;
	font-size:14px;
}
.item-desc img {
	border:1px solid #cccccc;
	margin:5px 15px 5px 10px;
	float:left;
}
.item-desc h3 {
	margin-top:5px;
}
.item-desc p {
	padding-top:0;
}
.item-headprice,
.item-price {
	width:69px;
	float:left;
	border-bottom:1px solid #cccccc;
	height:52px;
	text-align:center;
	font-size:14px;
	padding-top:40px;
}
.item-headprice {
	height:45px;
	padding:0;
	font-size:11px;
}
.pck {
	display:block;
	width:88px;
	height:28px;
	float:left;
	margin-right:4px;
	font-size:14px;
	font-weight:bold;
	text-align:center;
	padding-top:13px;
	color:#000000;
}
.bronze {background:url(/front/imgs/bronze.png) center center no-repeat;}
.bronzeplus {background:url(/front/imgs/bronze-plus.png) center center no-repeat;}
.silver {background:url(/front/imgs/silver.png) center center no-repeat;}
.gold {background:url(/front/imgs/gold.png) center center no-repeat;}
.platinum {background:url(/front/imgs/platinum.png) center center no-repeat;}
.diamond {background:url(/front/imgs/diamond.png) center center no-repeat;}

div#rightCol {
	width:187px;
	float:right;
}

div#footer {
	font-size:14px;
	text-align:center;
	padding-top:15px;
	padding-bottom:15px;
	border-top:1px solid #cccccc;
	color:#999999;
}
div#footer a {
	color:#000000;
	text-decoration:none;
}
div#footer a:hover {
	text-decoration:underline;
}
/* ================================ BOXES ================================================= */

.box-grey,
.box1 {
	width:187px;
	background:url(/front/imgs/box1_middle.gif) top center repeat-y;
	margin-bottom:10px;
}
.box-grey_foot,
.box1-foot {
	background:url(/front/imgs/box1_bottom.gif) bottom left no-repeat;
	padding:5px;
	font-size:11px;
}
.box-grey_foot {
	font-size:15px;
	text-align:center;
	padding:5px 5px 15px 5px;
}
.box1 h3 {
	display:block;
	overflow:hidden;
	background:url(/front/imgs/box1_top.gif) top left no-repeat;
	width:187px;
	height:24px;
	color:#ffffff;
	font-size:14px;
	text-align:center;
	padding-top:7px;
}
.box1 h4 {
	font-size:13px;
	margin-top:5px;
}
.box-grey_top {
	background:url(/front/imgs/box2-top.png) top left no-repeat;
}
.chat {
	background:url(/front/imgs/chat.jpg) top left no-repeat;
	width:133px;
	height:119px;
	margin-bottom:10px;
	font-size:15px;
	padding:4px 27px;
}
.chat p {
	display:block;
	margin-top:3px;
}
.big-grey-box {
	width:581px;
	background:#e7e7e7 url(/front/imgs/box-grey-large-top.png) top left no-repeat;
	margin-bottom:10px;
}
.big-grey-box_in {
	background:url(/front/imgs/box-grey-large-bottom.png) bottom left no-repeat;
	padding:20px;
}
.big-grey-box h2 {
	font-size:28px;
	color:#666666;
	font-weight:normal;
	margin-bottom:15px;
	line-height:20px;
}
.box-grey2 {
	background:url(/front/imgs/box-grey2-mid.png) top left repeat-y;
	width:780px;
	clear:left;
	margin-bottom:15px;
}
.box-grey2-top {
	background:url(/front/imgs/box-grey2-top.png) top left no-repeat;
}
.box-grey2-bottom{
	background:url(/front/imgs/box-grey2-bottom.png) bottom left no-repeat;
	padding:10px 0;
}
.box-grey2 .head {
	padding:0 15px 20px 15px;
	border-bottom:1px solid #cccccc;
}
.box-2grey {
	background:url(/front/imgs/box-2grey-mid.png) top left repeat-y;
	width:780px;
	clear:left;
	margin-bottom:15px;
}
.box-2grey-top {
	background:url(/front/imgs/box-2grey-top.png) top left no-repeat;
}
.box-2grey-bottom {
	background:url(/front/imgs/box-2grey-bottom.png) bottom left no-repeat;
	padding:10px 0;
}
.box-white,
.box-white2 {
	background:url(/front/imgs/box-white-middle.png) top left repeat-y;
	width:581px;
	font-size:12px;
	margin-bottom:10px;
}
.box-white2 {
	background:url(/front/imgs/box-white2-middle.png) top left repeat-y;
}
.box-white-top,
.box-white2-top {
	background:url(/front/imgs/box-white-top.png) top left no-repeat;
}
.box-white-bottom,
.box-white2-bottom {
	background:url(/front/imgs/box-white-bottom.png) bottom left no-repeat;
	padding:5px 10px 15px 10px;
}
.box-white3 {
	width:362px;
	margin-bottom:10px;
	background:url(/front/imgs/box-white3-mid.png) top left repeat-y;
}
.box-white3-top {
	background:url(/front/imgs/box-white3-top.png) top left no-repeat;
}
.box-white3-bottom {
	background:url(/front/imgs/box-white3-bottom.png) bottom left no-repeat;
	padding:10px;
}

/* ================================ Matthew Journey page boxes - 10th Nov 2009 ================================================= */
.box-whitej {
	width:362px;
	margin-bottom:2px;
	background:url(/front/imgs/box-white3-mid.png) top left repeat-y;
}
.box-whitej-top {
	background:url(/front/imgs/box-white3-top.png) top left no-repeat;
}
.box-whitej-bottom {
	background:url(/front/imgs/box-white3-bottom.png) bottom left no-repeat;
	padding:5px;
}
/* ================================ Matthew Journey page boxes - 10th Nov 2009 ================================================= */



.box-white-bottom ul {
	padding:0;
	margin:0;
	list-style:none;
	color:#999999;
}
.box-white-bottom .col1 {
	width:178px;
	float:left;
	padding-left:10px
}
.box-white-bottom .col2 {
	width:190px;
	padding-left:20px;
	float:left;
}
.box-white-bottom .col3 {
	width:143px;
	float:left;
	padding-left:20px;
}
.box-dblwhite {
	background:url(/front/imgs/box-dblwhite-mid.png) top left repeat-y;
}
.box-dblwhite-top {
	background:url(/front/imgs/box-dblwhite-top.png) top left no-repeat;
}
.box-dblwhite-bottom {
	background:url(/front/imgs/box-dblwhite-bottom.png) bottom left no-repeat;
	padding:15px;
}
.box-lrgwhite {
	background:url(/front/imgs/box-whitelrg-middle.png) top left repeat-y;
	margin-bottom:10px;
	clear:left;
	width:780px;
}
.box-lrgwhite-top {
	background:url(/front/imgs/box-whitelrg-top.png) top left no-repeat;
}
.box-lrgwhite-bottom {
	background:url(/front/imgs/box-whitelrg-bottom.png) bottom left no-repeat;
	padding:15px;
}
.box-yellow {
	background:url(/front/imgs/box-yellow-mid.png) top left repeat-y;
	width:380px;
	margin-bottom:10px;
}
.box-yellow-top {
	background:url(/front/imgs/box-yellow-top.png) top left no-repeat;
}
.box-yellow-bottom {
	background:url(/front/imgs/box-yellow-bottom.png) bottom left no-repeat;
	padding:15px;
}
.lefthalf {
	width:250px;
	float:left;
}
.righthalf {
	width:250px;
	float:right;
}
.progressbar {
	width:580px;
	height:85px;
	margin-bottom:10px;
	color:#666666;
	font-size:28px;
	text-align:center;
	padding-top:5px;
}
.step1 {background:url(/front/imgs/progress-1.png) top left no-repeat;}
.step1a {background:url(/front/imgs/progress-1a.png) top left no-repeat; height:38px;}
.step2 {background:url(/front/imgs/progress-2.png) top left no-repeat; height:38px;}
.step3 {background:url(/front/imgs/progress-3.png) top left no-repeat; height:38px;}
.step4 {background:url(/front/imgs/progress-4.png) top left no-repeat; height:38px;}

.progressbar2 {
	width:980px;
	height:21px;
	margin-bottom:5px;
}
.dash-step4 {background:url(/front/imgs/dash-step4.png) top left no-repeat;}
.dash-step5 {background:url(/front/imgs/dash-step5.png) top left no-repeat;}
.dash-step8 {background:url(/front/imgs/dash-step8.png) top left no-repeat;}

.box-blue {
	background:#cce5ff url(/front/imgs/box-blue-top.png) top left no-repeat;
	width:681px;
	margin-bottom:15px;
}
.box-blue-bottom {
	background:url(/front/imgs/box-blue-bottom.png) bottom left no-repeat;
	padding:15px;
}
.box-pink {
	background:#ffd1b2 url(/front/imgs/box-ping-top.png) top left no-repeat;
	width:681px;
	margin-bottom:15px;
}
.box-pink-bottom {
	background:url(/front/imgs/box-pink-bottom.png) bottom left no-repeat;
	padding:15px;
}
.pinkbox,
.yellowbox,
.bluebox {
	background:url(/front/imgs/blue-box.png) top left no-repeat;
	width:756px;
	height:19px;
	margin-bottom:10px;
	font-size:16px;
	padding:12px;
}
.pinkbox {
	background:url(/front/imgs/pink-box.png) top left no-repeat;
}
.yellowbox {
	background:url(/front/imgs/yellow-box.png) top left no-repeat;
}
.box-shaded {
	background:url(/front/imgs/box-shaded.png) top left no-repeat;
	display:block;
	width:209px;
	height:153px;
	float:left;
	margin:0 20px 20px 0;
	padding:15px;
	font-size:13px;
	color:#000000;
	text-decoration:none;
}
/* ================================ Nick Senkevich new action backgrounds - 10th Nov 2009 ================================================= */

.box-shaded2 {
	background:url(/front/imgs/dashboard-box.png) top left no-repeat;
	display:block;
	width:209px;
	height:80px;
	float:left;
	margin:0 20px 20px 0;
	padding: 15px 15px 3px 15px;
	font-size:13px;
	color:#000000;
	text-decoration:none;
}
.annualreturn {
	background:url(/front/imgs/dashboard-annualreturn.jpg) top left no-repeat;
}
.businesslibrary {
    background:url(/front/imgs/dashboard-businesslibrary.jpg) top left no-repeat;
}
.formacompany {
    background:url(/front/imgs/dash-formacompany.jpg) top left no-repeat;
}
.importcompany {
    background:url(/front/imgs/dashboard-importcompany.jpg) top left no-repeat;
}
.statutoryforms {
    background:url(/front/imgs/dashboard-statutoryforms.jpg) top left no-repeat;
}
.orderhistory {
    background:url(/front/imgs/dashboard-orderhistory.jpg) top left no-repeat;
}
.mydetails {
    background:url(/front/imgs/dashboard-mydetails.jpg) top left no-repeat;
}
.mycredit {
    background:url(/front/imgs/dashboard-mycredit.jpg) top left no-repeat;
}
.mycompanies {
    background:url(/front/imgs/dashboard-mycompanies.jpg) top left no-repeat;
}
.action_box_size{
width:170px;
}

/*  ================================ End ================================================= */

.box-shaded-small {
	background:url(/front/imgs/box-small-shaded.png) top left no-repeat;
	width:295px;
	height:73px;
	margin-bottom:10px;
	padding:10px;
}
.box-mid-gradient {
	background:url(/front/imgs/box-mid-gradient.png) center center no-repeat;
	width:311px;
	height:101px;
	float:right;
	margin-right:20px;
	padding:15px;
}
.box-orange {
	background:#ff6600 url(/front/imgs/box-orange-top.png) top left no-repeat;
	width:581px;
	margin-bottom:10px;
}
.box-orange-bottom {
	background:url(/front/imgs/box-orange-bottom.png) bottom left no-repeat;
	padding:15px;
	font-size:14px;
	color:#ffffff;
}

.tick-upgr {
	clear:left;
	font-size:16px;
	display:block;
	width:250px;
}

/* ============================ LISTS ====================================================== */

.special-list {
	padding:0;
	margin:0;
	list-style:none;
	font-size:15px;
	display:block;
}
.special-list li {
	border-bottom:1px solid #cccccc;
	padding:6px 0;
}
.special-list li.last {
	border:0;
}
.special-list li a {
	text-decoration:none;
	color:#000000;
	display:block;
	margin:0 8px 0 8px;
	background:url(/front/imgs/arrow-left.gif) right center no-repeat;
}
.special-list li a:hover {
	color:#666666;
}
.why-us {
	padding:0;
	margin:0;
	list-style:none;
}
.why-us li {
	display:block;
	font-size:15px;
	font-weight:bold;
	margin-top:15px;
	padding-left:15px;
	background:url(/front/imgs/bullet-orange.png) left center no-repeat;
}
/* ============================ BUTTONS ============================================ */

.btn_more,
.btn_submit,
.btn_back2,
.btn_forward2,
.btn_back {
	display:block;
	background:url(/front/imgs/btn_more.gif) center center no-repeat;
	width:155px;
	height:24px;
	margin:auto;
	color:#ffffff;
	text-decoration:none;
	font-size:15px;
	font-weight:bold;
	text-align:center;
	padding:5px 5px 0 0;
}
.btn_back2 {
	background:url(/front/imgs/btn_back2.png) center center no-repeat;
	margin: 0 0 28px 0;
	padding:5px 0 0 5px;
	width:135px;
}
.btn_forward2 {
	background:url(/front/imgs/btn-forward2.png) center center no-repeat;
	padding:5px 5px 0 0;
	width:135px;
}
.btn_back {
	background:url(/front/imgs/btn_back.png) center center no-repeat;
	padding:5px 0 0 5px;
	width:135px;
}
.btn_submit {
	width:160px;
	height:29px;
	margin:0;
	border:0;
	padding:0;
	line-height:13px;
	cursor:pointer;
}
.btn_go {
	background:url(/front/imgs/btn_go.png) top left no-repeat;
	width:39px;
	height:23px;
	display:block;
	text-indent:-9999px;
}

/* ============================ FORMS ============================================ */

#company-search {
	display:block;
	width:380px;
}
#company-search div.inputbox {
	background:url(/front/imgs/searchbox.png) top left no-repeat;
	width:305px;
	height:20px;
	padding:5px 0 0 3px;
}
div.inputbox2 {
	background:url(/front/imgs/searchbox2.png) top left no-repeat;
	width:244px;
	height:20px;
	padding:5px 0 0 3px;
}
#company-search input.searchbox {
	width:300px;
	background:transparent;
	border:none transparent;
}
input.searchbox2 {
	width:240px;
	background:transparent;
	border:none transparent;
}
.customer input {
	float:left;
	margin-bottom:15px;
}
.customer label {
	float:left;
	margin-bottom:5px;
	clear:left;
	display:block;
	width:200px;
}
.field220 {
	width:220px;
}
.field110 {width:110px;}
.field45 {width:45px;}
.field60 {width:60px;}
.field150 {width:150px;}

/* ============================ TABLES ============================================ */

/*============================= Added by Matthew 10th Nov 2009===================== */
.formtablejourney {
	margin:1px 0;
}
.formtablejourney td {
	padding:1px;
}
/*============================= Added by Matthew 10th Nov 2009===================== */

.formtable {
	margin:10px 0;
}
.formtable td {
	padding:5px;
}
.classictable {
	margin-bottom:10px;
}
.classictable th {
	border-bottom:1px solid #cccccc;
	padding:2px;
}
.classictable td {
	padding:2px;
}
.formtable th {
	font-size:14px;
	font-weight:bold;
	padding:5px;
	border-bottom:1px solid #cccccc;
}
.matrix {
	margin:10px 0;
	border-top:1px solid #cccccc;
	border-left:1px solid #cccccc;
	border-bottom:1px solid #cccccc;
}
.matrix td {
	padding:5px;
	border-right:1px solid #cccccc;
}
.matrix td.noborder {
	border:0px;
}
.matrix tr.odd td.check {
	background:#ebebeb url(/front/imgs/check1.png) center center no-repeat;
}
.matrix tr td.check {
	background:url(/front/imgs/check2.png) center center no-repeat;
}
.odd {
	background-color:#ebebeb;
}
.bbottom {
	border-bottom:1px solid #cccccc;
}
.b-bronze {background:#83705d url(/front/imgs/b_bronze.png) top left repeat-x;}
.b-bronze-plus {background:#a28971 url(/front/imgs/b_bronze-plus.png) top left repeat-x;}
.b-silver {background:#8f8e8f url(/front/imgs/b_silver.png) top left repeat-x;}
.b-gold {background:#a9915a url(/front/imgs/b_gold.png) top left repeat-x;}
.b-platinum {background:#a6a5a6 url(/front/imgs/b_platinum.png) top left repeat-x;}
.b-diamant {background:#f2f2f2 url(/front/imgs/b_diamond.png) top left repeat-x;}

.bbottom a {
color:#000000;
outline:medium none;
text-decoration:none;
}
.strike {
	color:#666666;
	text-decoration:line-through;
}
.my-companies {
	border:1px solid #cccccc;
	margin-bottom:15px;
}
.my-companies th {
	font-size:16px;
	font-weight:bold;
	color:#ff6600;
}
.my-companies td,
.my-companies th {
	padding:5px;
}
.dashtable {
	margin-bottom:20px;
	text-align:center;
}
.dashtable a {
	color:#000000;
	text-decoration:none;
}
.dashtable a:hover {
	text-decoration:underline;
}

/* ============================ GENERAL CLASSES ============================================ */

.clear {clear:both;}
.clearl {clear:left;}
.clearr {clear:right;}
div.clear {clear:both; height:0px;}
.h180 {height:180px;}
.auto-over {overflow:auto;}
div.hr {height:1px; background:#cccccc; margin:10px 0;}

.mbottom20 {margin-bottom:20px;}
.mtop20 {margin-top:20px;}
.mright20 {margin-right:20px;}
.mleft20 {margin-left:20px;}
.mright {margin-right:10px;}
.mleft {margin-left:10px;}
.mbottom {margin-bottom:10px;}
.mtop {margin-top:10px;}
.mbottom_small {margin-bottom:5px;}
.mtop_small {margin-top:5px;}
.mnone {margin:0px;}

.noborder {border:0;}

.fleft {float:left;}
.fright {float:right;}

.nopleft {padding-left:0px;}
.nopright {padding-right:0px;}
.noptop {padding-top:0px;}
.pbottom15 {padding-bottom:15px;}
.ptop15 {padding-top:15px;}
.padding10 {padding:10px;}

.txtcenter {text-align:center;}
.txtleft {text-align:left;}

.disclosure {font-size:10px;}
.midfont {font-size:14px;}
.normalfont {font-size:12px;}
.largefont {font-size:16px;}
.hugefont {font-size:22px;}
.required {font-size:11px; color:#666666;}
.orange {color:#ff6600;}
.yellow {color:#ffcc00;}
.grey {color:#cccccc;}
.grey2 {color:#999999;}
.white {color:#ffffff;}
.package {font-size:28px; font-weight:bold;}

.blackBorder{background: #f2f2f2 url(/front/imgs/blackborder.png) top left repeat-x;height:12px;}
.orangeBorder{background: #f2f2f2 url(/front/imgs/orangeborder.png) top left repeat-x;height:12px;}
.greyBorder{background-color: #ccc;height:12px;}
.emptyhr{height:9px;}
.orangetriangle{background: #fff url(/front/imgs/orangetriangle.png) top center no-repeat;height:9px;}

#continueprocess {
	background:url(/front/imgs/btn-forward2.png) center center no-repeat;
	padding:0 0 5px 0;
	width:155px;
}

div.help-button {
    margin-left: 15px;
    margin-right: 15px;
    float: left;
    width: 25px;
}

div.help-button em {
    position: absolute;
    background-color: #eeeeee;
    border: 1px solid #cccccc;
    padding: 5px;
    width: 300px;
    z-index: 2;
    display: none;
}

div.help-button2 {
    margin-left: 15px;
    margin-right: 15px;
    float: left;
    width: 25px;
}

div.help-button2 em {
    position: absolute;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    padding: 5px;
    width: 240px;
    z-index: 2;
    display: none;
}

a.help-icon {
    width: 24px;
    height: 24px;
    background: url(/front/imgs/help_24.gif) no-repeat 0 0;
    text-indent: -9999px;
    display: block;
}
a.help-icon2 {
	width: 24px;
    height: 24px;
	background: url(/front/imgs/question.gif) no-repeat 0 -5px;
	text-indent: -9999px;
   	display: block;
}
div.help-button-matrix {
    float: right;
    width: 22px;
}
div.help-button-matrix em {
    position: absolute;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    padding: 5px;
    width: 240px;
    z-index: 2;
    display: none;
}
a.help-icon-grey-bg {
	width: 22px;
    height: 16px;
	background: url(/front/imgs/matrixq_greybg.gif) no-repeat 0 -5px;
	text-indent: -9999px;
   	display: block;
}
a.help-icon-white-bg {
	width: 22px;
    height: 16px;
	background: url(/front/imgs/matrixq_whitebg.jpg) no-repeat 0 -5px;
	text-indent: -9999px;
   	display: block;
}
span.help-icon-grey-bg2 {
	width: 22px;
    height: 16px;
	background: url(/front/imgs/matrixq_greybg.gif) no-repeat 0 -5px;
	text-indent: -9999px;
   	display: block;
    cursor: pointer;
}
span.help-icon-white-bg2 {
	width: 22px;
    height: 16px;
	background: url(/front/imgs/matrixq_whitebg.jpg) no-repeat 0 -5px;
	text-indent: -9999px;
   	display: block;
    cursor: pointer;
}
#custom{
	height: 24px;
}
#support{
	height: 24px;
}
#paypal{
	height: 24px;
}
#google{
	height: 24px;
}

.livesuprt{
	float:right;
	position:relative;
}
.redmsg{
	color:red;
}
.formprocesstitle{
	position:relative;
	float: left;
}

input.link {
	background: none;
	border: 0;
	text-decoration: underline;
	cursor: pointer;
	margin: 0;
	padding: 0;
	display: inline;
	color: #006699;
}
	input.link:hover {
		text-decoration: none;
	}



/** BARCLAYS BANKING ON UPGRADE PAGE **/

.barclays-box {
		border: 1px solid #cdcdcd;
		margin: 0 0 15px 0;
		padding: 10px 50px 10px 10px;
		background: url('/front/imgs/barclays_logo_small.jpg') no-repeat 232px 53px #f2fafd;
		line-height: 160%
	}
		.barclays-box span {
			font-size: 11px;
		}

/** BARCLAYS BANKING ON CU SUMMARY PAGE **/

.cu-summary-barclays {
	position: absolute; top: 0; right: 0;
	border: 1px solid #cbcbcb;
	width: 180px;
}
	.cu-summary-barclays .header {
		background: #ebebeb;
		border-bottom: 1px solid #cbcbcb;
		padding: 5px;
		text-align: center;
		font-size: 12px;
	}
	.cu-summary-barclays .content {
		background: #f2fafd url('/front/imgs/barclays_44x48.gif') no-repeat 130px 5px;
		padding: 5px 5px 10px 5px;
	}
		.cu-summary-barclays .content input.link {
			color: #fe6e15;
			font-weight: bold;
			font-size: 13px;
			position: absolute;
			left: 30px;
			bottom: 5px;
		}

.barclays-banner{
	background: #ffffff url('/front/imgs/barclays_banner.png') no-repeat;
	width:426px;
	height:63px;
	float:right;
	position:relative;
	margin:-12px 0;
}
	.barclays-banner p{
		color:#666666;
		padding: 40px 0 8px 10px;
		margin-bottom:10px;
	}
		.barclays-banner a{
			text-decoration:none;
		}
.barclays-hp-banner{
	background: #ffffff url('/front/imgs/barclays_hp_banner.png') no-repeat;
	width:187px;
	height:148px;
	margin-top:10px;
}
	.barclays-hp-banner span {
		display: block;
		color:#666666;
		padding: 76px 4px 8px 10px;
	}
		.barclays-hp-banner a{
			text-decoration:none;
		}
.need-help{
	width:187px;
	background:url(/front/imgs/help-box_middle.gif) top center repeat-y;
	margin-bottom:10px;
}
.need-help-foot {
	background:url(/front/imgs/help-box_bottom.gif) bottom left no-repeat;
	padding:5px;
	font-size:11px;
}
.need-help h3 {
	display:block;
	overflow:hidden;
	background:url(/front/imgs/box1_top.gif) top left no-repeat;
	width:187px;
	height:24px;
	color:#ffffff;
	font-size:14px;
	text-align:center;
	padding-top:7px;
}
.need-help-list{
	padding:0;
	margin:0;
	list-style:none;
	font-size:12px;
	display:block;
}
.need-help a{
	text-decoration:none;
}
.need-help img{
	padding:2px 10px 2px 5px;
	vertical-align:middle;
	height:35px;
	width:35px;
}


/*=== CU summary page ===*/

.cu-summary-page .cu-summary-container {
		position: relative;
	}
		.cu-summary-page .cu-summary-container h1 {
			width: 530px;
		}
		.cu-summary-page .cu-summary-container .menu {
			position: absolute;
			top: 0;
			right: 0;
			z-index: 100;
		}
			.cu-summary-page .cu-summary-container .menu .buttons {
				text-align: right;
				display: none;
			}
				.cu-summary-page .cu-summary-container .menu .buttons .text {
					color: black;
					font-weight: normal;
				}
			.cu-summary-page .cu-summary-container .menu ul {
				list-style-type: none;
				padding: 5px 10px 5px 10px;
				margin: 0px 4px 0 0;
				display: none;
			}
				.cu-summary-page .cu-summary-container .menu ul li {
					line-height: 25px;
				}
					.cu-summary-page .cu-summary-container .menu ul li a {
						text-decoration: none;
						font-weight: bold;
					}
						.cu-summary-page .cu-summary-container .menu ul li a:hover {
							text-decoration: underline;
						}
	.cu-summary-page .flash.info2.barclays {
		background: #F2FAFD url('/front/imgs/barclays_44x48.gif')  730px 3px no-repeat;
		border: 1px solid #CDCDCD;
		margin-left: 0;
		width: 515px;
		padding-right: 250px;
		padding-top: 10px;
		position: relative;
	}
		.cu-summary-page .flash.info2.barclays a.apply {
			font-size: 18px;
			font-weight: bold;
			position: absolute;
			right: 120px;
			top: 15px;
		}
	.cu-summary-page a.ui-widget-content {
		padding: 1px 3px 1px 3px;
		text-decoration: none;
		font-size: 12px;
	}
	.cu-summary-page h2 {
		position: relative;
		padding-right: 0;
	}
		.cu-summary-page h2 a.ui-widget-content {
			position: absolute;
			font-size: 12px;
			font-weight: normal;
			top: 4px;
			margin: 0 0 0 5px;
		}
	.cu-summary-page .barclays-explanation {
		margin: 20px 0 20px 0;
		color: #444;
		font-size: 11px;
	}


/* === SEARCH DEFAULT PAGE === */

.search-default-page .new-search {
	margin: 0 0 20px 0;
}
	.search-default-page .new-search h2 {
		margin: 0;
		padding: 0;
		font-size: 16px;
	}
	.search-default-page .new-search .left {
		float: left;
		width: 446px;
		height: 66px;
		background: url('/front/imgs/matrix-search-box.jpg') no-repeat;
		padding: 10px;
		margin: 0;
		position: relative;
	}
		.search-default-page .new-search .left input.searchbox2 {
			border: 1px solid #abadb3;
			position: absolute;
			left: 10px;
			top: 55px;
			width: 300px;
		}
		.search-default-page .new-search .left input.go_search {
			position: absolute;
			left: 325px;
			top: 56px;
		}
		.search-default-page .new-search .right {
			float: right;
			width: 281px;
			height: 66px;
			background: url('/front/imgs/matrix-search-step2.jpg') no-repeat;
			padding: 10px;
			margin: 0 0 0 0;
		}


/* === SPECIAL JARED'S TABLE === */

table.ginger-ninja-table {
	background-color: white;
	border-left: 1px solid #ebebeb;
	border-top: 1px solid #ebebeb;
}
	table.ginger-ninja-table td {
		border-right: 1px solid #ebebeb;
		border-bottom: 1px solid #ebebeb;
	}


/* === HOME LEFT COLUMN BLOG ARTICLES === */

h4.blog {
	font-size: 12px;
    padding: 0;
    margin: 0 0 3px 0;
}
p.blog {
	padding: 0;
	margin: 0 0 10px 0;
}

/* === REVIEWS TESTIMONIALS PAGE === */

.reviews-testimonials-page {
	padding-bottom: 25px;
}
	.reviews-testimonials-page .text-box {
		width: 775px;
	}
		.reviews-testimonials-page .text-box .text {
			float: left;
			width: 570px;
		}
		.reviews-testimonials-page .text-box .image {
			float: right;
			width: 200px;
			text-align: right;
			margin-top: -45px;
			position: relative;
		}
			.reviews-testimonials-page .text-box .image .value {
				position: absolute;
				color: white;
				font-size: 100px;
				top: 5px;
				left: 45px;
			}
		.reviews-testimonials-page .text-box a {
			text-decoration: none;
		}
	.reviews-testimonials-page table.my-companies {
		margin-top: 20px;
	}

/* === REVIEWS SATISFACTION PAGE === */

.reviews-satisfaction-page {
	padding-bottom: 25px;
}
	.reviews-satisfaction-page .text-box {
		width: 775px;
	}
		.reviews-satisfaction-page .text-box .text {
			float: left;
			width: 570px;
		}
		.reviews-satisfaction-page .text-box .image {
			float: right;
			width: 200px;
			text-align: right;
			margin-top: -45px;
			position: relative;
		}
			.reviews-satisfaction-page .text-box .image .value {
				position: absolute;
				color: white;
				font-size: 100px;
				top: 5px;
				left: 45px;
			}
		.reviews-satisfaction-page .text-box a {
			text-decoration: none;
		}
		.reviews-satisfaction-page .box-chart {
			width: 775px;
		}
			.reviews-satisfaction-page .box-chart .left {
				float: left;
				width: 450px;
			}
		.reviews-satisfaction-page .grid2 td {
			color: #666;
		}

                /* === FEEDBACK PAGE === */
                .feedback{
                    margin:auto;
                    width:450px;
                    border:0px solid blue;
                    overflow: visible;
                }
                .feedback .number_format {
                    font-family: Arial,Helvetica,sans-serif;
                    font-size: 20px;
                    font-weight: bold;
                    padding-right:12px;
                }
                .feedback .lbl_format {
                    padding-top: 1px;
                    font-weight: bold;
                    font-size: 13px;
                    color:#686868;
                }
                .feedback .lbl_format label {
                    font-size: 13px;
                    font-weight: bold;
                    color:#686868;
                }
                .feedback h1 {
                    font-weight: bold;
                    padding-bottom: 10px;
                    padding-top:15px;
                    font-size: 22px;
                    color: #080808;
                }

                .feedback .fright{
                   float: left;
                   padding: 0 8px 0px 0;
                }
                .feedback .td_format{
                      padding-bottom: 0px;
                }
                .feedback .feeddback_radio{
                    float:left;
                }
                .feedback_text{
                    padding-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    font-style: italic
                }
                .feedback_text p{
                    margin:0;
                    padding:0;
                }
                .feedback h1 .heart{
                    color:#fd6000;
                    font-size: 24px;
                }
                .feedback .radioSet {
                    padding-top:20px;
                    padding-bottom:15px;
                    padding-left:25px;
                }

                .feedback .radioSet label {
                    color: #686868;
                    font-size: 17px;
                    font-weight: bold;
                    padding-right: 25px;
                }

               .feedback .radioSet input{
                   vertical-align: top;
                   height: 19px;
               }

                .feedback label[for="radioPositive"]{
                    color: #686868;

                }
                .feedback label[for="radioNeutral"]{
                    color: #686868;
                }
                .feedback label[for="radioNegative"]{
                    color: #686868;
                }
                .feedback .radioSet .selected {
                    color:#FF6600
                }

                .popup div.flash.info{
                    margin:auto;
                    width:550px;
                }
                /* === Jorney PAGE === */

    .Financial_Services .moreInfo a{
        color: #a62335;
    }
    .Financial_Services .header{
        color: #a62335;
        background-image:url('/front/imgs/financial-services.png');
    }

    .Marketing .moreInfo a{
        color: #3aa323;
    }
    .Marketing .header  {
        color: #3aa323;
        background-image:url('/front/imgs/marketing.png');
    }

    .Online_Technology .moreInfo a{
        color: #2322aa;
    }
    .Online_Technology .header {
        color: #2322aa;
        background-image:url('/front/imgs/online-tech.png');
    }

    .Business_Services .moreInfo a{
        color: #227ba7;
    }
    .Business_Services .header {
        color: #227ba7;
        background-image:url('/front/imgs/business-services.png');
    }

.cf-choose-bank-account {
    font-size: 14px;
    color: #666
}

.cf-choose-bank-account .cf-barcklays-account,.cf-choose-bank-account .cf-hsbc-account {
    border: 1px solid #CCCCCC;
    padding: 15px;
    background-color: #f8f8f8;
}

.cf-choose-bank-account p.cf-p {
    margin-bottom: 20px;
    color: #000;
}
.cf-choose-bank-account .product-img {
    width: 78px;
    height: 78px;
    border: 1px solid #CCCCCC;
    float: left;
    margin-right: 30px;
}

.cf-choose-bank-account .product {
    float: left;
    margin-right: 30px;
    padding-top: 10px;
    width: 280px;
}
.cf-choose-bank-account .product h4 {
    font-size: 28px;
    color: #000;
}
.cf-choose-bank-account .cf-more-info{
    float: left;
    margin-right: 30px;
    padding-top: 35px;
}
.cf-choose-bank-account .select{
    float: left;
    padding-top:25px;
}
.cf-choose-bank-account .no-top-border {
    border-top: 0;
}

.cf-choose-bank-account .btn-choose {
    background:url(/front/imgs/orangebtn-greybg.png) center center no-repeat;

}

/* === Matrix PAGE === */

    .new-matrix-search{
        border:1px solid #b7b7b7;
        margin-top: 20px;
        margin-bottom: 20px;
        padding-top: 20px;
        padding-bottom: 20px;
    }

    .search-result-name{
        padding-left: 50px;
    }

    .search-result-name .result-name-text {
        float:left;
        width: 410px;
        height:auto;
    }

    .search-result-name .result-name-time {
        float:right;
        width:297px;
        height: 64px;
        margin-left:10px;
        margin-right: 30px;
        background:url(/front/imgs/matrix/timmer.jpg) center center no-repeat;
    }

    .search-result-name .result-name-text p {
        font-size: 20px;
        color:#000;
    }
    .search-result-name .result-name-text p  span{}

    .search-result-name .result-name-tick{
        width: 24px;
        height: 24px;
        float: left;
        margin-top:4px;
        margin-left:5px;
        margin-right: 40px;
        background:url(/front/imgs/matrix/silver-odd-tick.jpg) center center no-repeat;
    }


    .matrix-pakages-table{
        width: 895px;background-color: white;margin: auto;margin-top:20px;
        height: auto;
    }
    .matrix-pakages-table-head{
        height: 21px;
        background:url(/front/imgs/matrix/headertop.jpg) left 2px no-repeat;
    }


    .matrix-package-head-last{
        border-right: 1px solid blue;
    }

    .matrix-silver-body{
        background:url(/front/imgs/matrix/mid-silver-td.jpg) top center repeat-y;
    }

    .no-left-border{
        border-left: none;
    }


    .matrix-column-top{
        width: 147px;
        height: auto;
        float: left;
    }

    .matrix-column-top-silver {
        float: left;
        width: 156px;
    }

    .matrix-package-head, .matrix-package-bottom{

        border-left: 1px solid #b0d8ec;
    }

    .matrix-package-head{
        background:url(/front/imgs/matrix/toptablegradient.jpg) top center repeat-x #f0eee5;
         border-top: 1px solid #b0d8ec;
    }

    .matrix-package-bottom {
        background-color: #f0eee5;
         border-bottom: 1px solid #b0d8ec;
    }


    .matrix-silver-head{
        background:url(/front/imgs/matrix/silver-top.jpg) bottom center repeat-x;
        height: 19px;
    }

    .matrix-package-name{
        font-family: arial, Helvetica,sans-serif;
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        color: #004e75;
        text-shadow: 1px 1px 1px #C1D9E5;
        padding-bottom: 10px;
        padding-top: 10px;

   }

   .matrix-package-text{
       font-family: arial, Helvetica,sans-serif;
        text-align: center;
        font-size: 13px;
        font-weight: normal;
        line-height: 19px;
        color: #464646;
        height: 30px;
        padding-bottom: 19px;
        vertical-align: central;
   }
   .matrix-package-text-price{
       font-family: arial, Helvetica,sans-serif;
        text-align: center;
        line-height: 15px;
        height: 15px;
        margin-bottom: 8px;
        color: #000;

   }

   .matrix-last-top{
       padding-bottom: 12px;
   }

   .silver-black-color{
       color: #000;
   }
   .package-separator{
       background:url(/front/imgs/matrix/separator-gradient.jpg) top center repeat-x;
       height: 2px;
       width: 130px;
       margin: auto;
       margin-top:0px;
       margin-bottom: 15px;
  }
  .matrix-package-head p {
      padding-top: 3px;
  }

  .matrix-package-price{
      text-align: left;
      padding-left:8px;
      padding-top: 5px;
      padding-bottom: 5px;
      font-size: 12px;
      font-weight: bold;
      color: #818283;
  }

  .matrix-btn {
	display:block;
	background:url(/front/imgs/matrix/btn.jpg) -1px -1px no-repeat;
	width:124px;
	height:27px;
    line-height: 15px;
	margin:auto;
	color:#ffffff;
	text-decoration:none;
	font-size:13px;
	font-weight:bold;
	text-align:center;
	padding:5px 5px 0 0;
}

.matrix-btn-silver {
	display:block;
	background:url(/front/imgs/matrix/silver-btn.jpg) -1px -1px no-repeat;
	width:124px;
	height:27px;
    line-height: 15px;
	margin:auto;
	color:#000;
	text-decoration:none;
	font-size:13px;
	font-weight:bold;
	text-align:center;
	padding:5px 5px 0 0;
}


.matrix-btn-silver-small-up {
	display:block;
	background:url(/front/imgs/matrix/btn-silver-small.jpg) bottom left no-repeat;
	width:89px;
	height:20px;
    line-height: 20px;
	margin:auto;
	color:#000;
	text-decoration:none;
	font-size:12px;
	font-weight:bold;
	text-align:center;
	padding:5px 5px 0 0;
    margin-left:9px;
}

.matrix-btn-small-up {
	display:block;
	background:url(/front/imgs/matrix/btn-small.jpg) bottom left no-repeat;
	width:89px;
	height:20px;
    line-height: 20px;
	margin:auto;
	color:#fff;
	text-decoration:none;
	font-size:12px;
	font-weight:bold;
	text-align:center;
	padding:5px 5px 0 0;
}

.padding-bottom {
    padding-bottom: 10px;
}

.matrix-last-pakage{
    border-right: 1px solid #b0d8ec;
}


.matrix-silver-bottom{
        background:url(/front/imgs/matrix/silver-bottom.jpg) bottom left no-repeat;
        height: 19px;
    }

    .matrix-middle-table {
        float:left;
        background-color: #f0eee5;
        width: 147px;
    }

    .matrix-middle-table-silver {
        background:url(/front/imgs/matrix/silver-gradient.jpg) top center repeat-y;
        float:left;
        width: 157px;
    }

    .matrix-last-pakage-midd{

}

.midd-matrix-gradient{
    background:url(/front/imgs/matrix/middle-gradient.jpg) top left repeat-y;
}


 .matrx-table-offers{
    border-collapse: collapse;
    width: 911px;
}
.matrx-table{
    border-collapse: collapse;
    width: 886px;
}


.matrx-table tr, .matrx-table td, .matrx-table th {
    padding: 0;
}

.matrix-table-head-up, .matrix-table-bottom-down {
    width: 147px;
    height: 19px;
}

.matrix-table-head {
    height: auto;
    width: 146px;
    background:#cfebf9 url(/front/imgs/matrix/pakage-top-gradient.jpg) top center repeat-x;
    border-top: 1px solid #bce2f6;
    border-left:1px solid #bce2f6;

}

.matrix-table-head-up-silver{
    background:url(/front/imgs/matrix/silver-top.jpg) bottom left no-repeat;
    height: 19px;
    width: 156px;
}

.matrix-table-head-silver, .matrix-table-bottom-silver {
    background:url(/front/imgs/matrix/silver-gradient.jpg) top left repeat-y;
}

.matrix-table-midd-silver{
    background:url(/front/imgs/matrix/silver-offer-gradient.jpg) -1px top repeat-y;
}

.matrix-table-bottom {
    background-color: #f4f2f2;
    border-bottom: 1px solid #bce2f6;
    border-left:1px solid #bce2f6;
}

.matrix-table-bottom-down-silver{
    background:url(/front/imgs/matrix/silver-bottom.jpg) top left no-repeat;
}

.matrix-head-info-pakages{
    background: #f4f2f2;
    border-left:1px solid #bce2f6;
    padding-bottom: 20px;
}

.pakage-info-p{
    text-align: center;
    padding-top: 5px;
    padding-bottom: 5px;
    width: 146px;
    padding-top: 20px;
}

.pakage-info-p-silver{
    text-align: center;
    padding-top: 5px;
    padding-bottom: 5px;
    width: 156px;
    padding-top: 20px;
}

.matrix-year-package{
    background:url(/front/imgs/matrix/year.jpg) top left no-repeat;
    color: #fff;
    width: 53px;
    height: 15px;
    line-height: 15px;
    margin: auto;
    font-size: 10px;
    font-weight: bold;
    text-align: center;
    vertical-align: middle;
    padding: 0px;

}

.matrix-year-text-package{
    position: absolute;
    color: #000;
    margin-top:-10px;
    margin-left: 35px;
    text-align: center;
    font-size:13px;
}

.matrix-price-size{
    font-size: 14px;
    font-weight: bold;
}

.pakage-line-separator{
    background: #34A0EB;
    border-left: 1px solid #34A0EB;
    border-right: 1px solid #34A0EB;
}

.pakage-line-separator p{
    text-align: left;
    color: white;
    font-size: 17px;
    padding: 4px;
    font-weight: normal;
    margin-left: 12px;
}

.pakage-line-separator p span{
    font-size: 12px;
}

.matrx-table-offers-info-head, .matrx-table-offers-silver-head, .matrx-table-offers-head{height: 13px;}
.matrx-table-offers-info-head, .matrx-table-offers-info-body, .matrx-offers-info-bottom-body, .matrx-offers-info-bottom, .matrx-table-offers-info-content{width: 300px;}
.matrx-table-offers-info-head{background-color: #fff}
.matrx-table-offers-info-body, .matrx-table-offers-body,.matrx-table-offers-body-silver{
    height: 69px;
}

.matrx-table-offers-info-body{
    border-top: 1px solid #c3bea9;
    border-left: 1px solid #c3bea9;
    border-bottom: 1px solid #c3bea9;
    background-color: white;
    text-align: center;
    font-size: 22px;
    color: #000;
    font-weight: bold;
}

.matrx-table-offers-head{
    width: 100px;
    background-color: #fff;

}
.matrx-table-offers-body{
    width: 100px;
    border-top: 1px solid #cad9d9;
    border-left: 1px solid #b8d4e0;
    border-bottom: 1px solid #c3bea9;
    background:url(/front/imgs/matrix/package-details-gradient-top.jpg) top left repeat-x;
}

.matrx-table-offers-silver-head{
    width:107px;
    background:#fff url(/front/imgs/matrix/silver-top-offer.jpg) bottom left no-repeat;
}
.matrx-table-offers-body-silver{
    width:107px;
    border-bottom: 1px solid #c3bea9;
    background:url(/front/imgs/matrix/silver-top-gradient.jpg) top left repeat-y;
}

/* === Matrix Offers bottom === */

.matrx-offers-info-bottom-body{border: 0px solid black;}
.matrx-table-offers-body-bottom{width: 100px;border-left: 1px solid #b8d4e0;border-bottom: 1px solid #b8d4e0;background-color: #f4f2f2;padding-top: 12px;}
.matrx-table-offers-body-bottom-silver{
    width: 100px;border: 0px solid black;
    padding-top: 8px;
    background:url(/front/imgs/matrix/silver-top-gradient.jpg) top left repeat-y;

}

.matrx-offers-info-bottom{height: 20px;}
.matrx-offers-bottom{width: 100px;height: 20px;}
.matrx-offers-bottom-silver{
    width: 100px;
    height: 13px;
    background:url(/front/imgs/matrix/silver-bottom-offer.jpg) top left no-repeat;
}


/* offer content */
.matrx-table-offers-info-content{border-left: 1px solid #c3bea9;height: auto;border-bottom: 1px solid #c3bea9;}
.matrx-table-offers-content{width: 100px;border-left: 1px solid #B8D4E0;height: auto; border-bottom: 1px solid #c3bea9;}
.matrx-table-offers-content-silver{
    background:url(/front/imgs/matrix/silver-midd-gradient.jpg) top left repeat-y;
    border-bottom: 1px solid #c3bea9;
}
.matrx-table-offers-content-silver-odd{
    background:url(/front/imgs/matrix/silver-odd-gradient.jpg) top left repeat-y;
    border-bottom: 1px solid #c3bea9;
}

.matrx-table-offers .oddRow{
    background: #f4f2f2;
}
.matrx-table-offers .evenRow{
    background: #e3e1e1;
}

.matrx-table-offers .oddRowColumn{
    background: #fefefe;
}
.matrx-table-offers .evenRowColumn{
    background: #ececec;
}

.matrix-pakage-tick, .matrix-pakage-tick-odd, .matrix-pakage-tick-silver, .matrix-pakage-tick-silver-odd{
    width: 24px;
    height: 24px;
    vertical-align: middle;
    margin: auto;
}

.matrix-pakage-tick{
    background: transparent url(/front/imgs/matrix/tick-lightbrown.png) top left no-repeat;

}

.matrix-pakage-tick-odd {
    background: transparent url(/front/imgs/matrix/tick-brown.png) top left no-repeat;
}

.matrix-pakage-tick-silver{
    background: transparent url(/front/imgs/matrix/silver-odd-tick.jpg) top left no-repeat;
}

.matrix-pakage-tick-silver-odd {
    background: transparent url(/front/imgs/matrix/silver-even-tick.jpg) top left no-repeat;
}


.matrix-package-name-offer{
    font-family: arial, Helvetica,sans-serif;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    color: #004e75;
    padding-top: 1px;
    text-shadow: 1px 1px 1px #C1D9E5;
}

.matrix-price-small,.matrix-value-small,.matrix-save-small{padding-left:8px;font-size: 12px;font-weight: bold;color: #726c5e; padding-top:4px;}
.matrix-value-silver-small,.matrix-price-silver-small,.matrix-save-silver-small{padding-left:13px;font-size: 12px;font-weight: bold;color: #000;}
.matrix-price-silver-small{padding-top: 6px;}
.matrix-value-silver-small{padding-top: 0px;}
.go-to-top{
    padding-top:7px;
    padding-left: 10px;
}

.ribbon {
    background: url(/front/imgs/matrix/ribbon.png) no-repeat scroll center top transparent;
    height: 80px;
    margin-left: 90px;
    margin-top: -17px;
    margin-right: 7px;
    position: absolute;
    width: 80px;
    z-index: 100;
}


.tooltip {
    display:none;
    padding: 4px;
    background:#f4f2f2 ;
    border:1px solid #a3c3cd;
    padding:3px;
    color: #464646;
    margin-left: 10px;
    padding: 10px;
    width: 170px;
    font-size:13px;
    -moz-box-shadow: 2px 2px 11px #666;
    -webkit-box-shadow: 2px 2px 11px #666;

}

.tooltip .img-arrow{
    background:transparent url(/front/imgs/matrix/tickbox-arrow.jpg) left center no-repeat;
    position: absolute ;
    top:50%;
    margin-top: -8px;
    left: -10px;
    width: 10px;
    height: 17px;
}


.result-name-time{
        overflow: hidden;
   }
    .result-name-time .same-day-txt{
        padding-left: 63px;
        font-weight: bold;
        font-size: 13px;
        padding-top: 15px;
   }

   .result-name-time .same-day-time{
       padding-top: 0;
       padding-left: 70px;
       font-weight: bold;
       font-size: 12px;
       padding-right: 30px;
       text-align: right;
   }

   .floatingHeader {
      position: fixed;
      top: 0;
      visibility: hidden;
    }

    .matrix-separator{
        width: 850px;
        margin-left: 35px;
    }
    .matrix-left-separator, .matrix-separator-text,.matrix-right-separator{
        float: left;
   }

   .matrix-right-separator,.matrix-left-separator{width: 115px;}
   .matrix-left-separator{
        height: 10px;
        border-left: 1px solid #a0caf9;
        border-top: 1px solid #a0caf9;
        margin-top: 10px;
   }
   .matrix-right-separator{
        height: 10px;
        border-right: 1px solid #a0caf9;
        border-top: 1px solid #a0caf9;
        margin-top: 10px;
   }
   .matrix-separator-text{color: white;padding-left: 5px;padding-right: 5px;padding-top: 3px;}
   .matrix-package-name-silver, .matrix-package-name-offer-silver{color: #000;text-shadow: -1px 1px 1px #E9E9E9;}
   .result-name-time-off
   {
        width:460px;
        margin-left:10px;
        float: left;
   }
   .result-name-time-off p {font-size: 18px;font-weight: bold;}
   .result-name-time-off ul{ width: 425px; padding: 0px; margin-left: 20px;}
   .result-name-time-off ul li { color: #464646; font-size: 12px; padding-left: 0px;}
   .result-name-time-off .separator-li {border-top:1px solid #dbdada;height: 1px;line-height: 1px;margin-top: 5px;margin-bottom: 5px;width: 434px;margin-left: 10px;}
   .why-use-cms-text{background:transparent url(/front/imgs/matrix/bullet-image.png) left center no-repeat;padding-left: 10px;margin-left: 10px;}
   .text-separator-up
   {
       margin-left: 40px;
       width: 450px;
       color:#434343;
       padding-left: 25px;
       background:transparent url(/front/imgs/matrix/exclamation.png) left top no-repeat;
   }

   .info-z-index{
       height: 61px;
       width: 323px;
       position: absolute; bottom: -10px; left: 41%;
       margin: auto;
       background:transparent url(/front/imgs/matrix/scroll-down.png) center top no-repeat;
   }

   .silver-top-shadow{
       text-shadow: -1px 1px 1px #E9E9E9;
   }

.ribbon{background:transparent; filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src=/front/imgs/matrix/ribbon.png, sizingMethod='scale');}



.payment-imgs {
    background: url("/front/imgs/pay-via-credit.png") no-repeat scroll left -2px transparent;
    border: 0 solid blue;
    float: left;
    height: 34px;
    width: 250px;
}
