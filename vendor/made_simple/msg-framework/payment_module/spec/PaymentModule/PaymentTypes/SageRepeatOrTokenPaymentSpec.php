<?php

namespace spec\PaymentModule\PaymentTypes;

use Exception;
use HttpClient\Requests\Request;
use HttpClient\Responses\Response;
use PaymentModule\Contracts\IPaymentData;
use PaymentModule\Loggers\RepeatPaymentLogger;
use PaymentModule\PaymentTypes\IPaymentType;
use SagePayToken\Token\Exception\FailedResponse;
use PaymentModule\Responses\PaymentResponse;
use SagePayRestApi\Exceptions\RequestException;
use PhpSpec\ObjectBehavior;

class SageRepeatOrTokenPaymentSpec extends ObjectBehavior
{
    /**
     * @var IPaymentType
     */
    private $sageRepeatPayment;

    /**
     * @var IPaymentType
     */
    private $paymentType;

    /**
     * @var RepeatPaymentLogger
     */
    private $logger;

    public function let(
        IPaymentType $sageRepeatPayment,
        IPaymentType $paymentType,
        RepeatPaymentLogger $logger
    )
    {
        $this->sageRepeatPayment = $sageRepeatPayment;
        $this->paymentType = $paymentType;
        $this->logger = $logger;

        $this->beConstructedWith($sageRepeatPayment, $paymentType, $logger);
    }

    public function it_should_make_repeat_payment(IPaymentData $data, PaymentResponse $paymentResponse)
    {
        $this->sageRepeatPayment->makePayment($data)->willReturn($paymentResponse);
        $this->makePayment($data)->shouldBe($paymentResponse);
    }

    public function it_should_make_token_payment(
        IPaymentData $data,
        PaymentResponse $paymentResponse
    )
    {
        $request = new Request('/test');
        $response = new Response(422, [], 'body');
        $requestException = new RequestException(new Exception(), $request, $response);

        $data->getInstanceId()->willReturn(1);
        $data->getContext()->willReturn('a');

        $context = ['log_reference' => 1, 'scenario' => 'a'];

        $this->sageRepeatPayment->makePayment($data)->willThrow($requestException);
        $this->logger->logGeneralError($requestException, $context)->shouldBeCalled();
        $this->logger->logTokenRequest($data, $context)->shouldBeCalled();
        $this->paymentType->makePayment($data)->willReturn($paymentResponse);
        $this->logger->logTokenResponse($paymentResponse, $context)->shouldBeCalled();
        $this->makePayment($data)->shouldBe($paymentResponse);
    }

    public function it_should_log_token_error(IPaymentData $data)
    {
        $request = new Request('/test');
        $response = new Response(422, [], 'body');
        $requestException = new RequestException(new Exception(), $request, $response);
        $failedResponse = new FailedResponse();

        $data->getInstanceId()->willReturn(1);
        $data->getContext()->willReturn('a');
        $context = ['log_reference' => 1, 'scenario' => 'a'];


        $this->sageRepeatPayment->makePayment($data)->willThrow($requestException);
        $this->logger->logGeneralError($requestException, $context)->shouldBeCalled();
        $this->logger->logTokenRequest($data, $context)->shouldBeCalled();
        $this->paymentType->makePayment($data)->willThrow($failedResponse);
        $this->logger->logTokenError($failedResponse, $context)->shouldBeCalled();
        $this->shouldThrow($failedResponse)->during('makePayment', [$data]);
    }

    public function it_should_log_general_error(IPaymentData $data)
    {
        $request = new Request('/test');
        $response = new Response(422, [], 'body');
        $requestException = new RequestException(new Exception(), $request, $response);
        $exception = new Exception();

        $data->getInstanceId()->willReturn(1);
        $data->getContext()->willReturn('a');
        $context = ['log_reference' => 1, 'scenario' => 'a'];

        $this->sageRepeatPayment->makePayment($data)->willThrow($requestException);
        $this->logger->logGeneralError($requestException, $context)->shouldBeCalled();
        $this->logger->logTokenRequest($data, $context)->shouldBeCalled();
        $this->paymentType->makePayment($data)->willThrow($exception);
        $this->logger->logGeneralError($exception, $context)->shouldBeCalled();
        $this->shouldThrow($exception)->during('makePayment', [$data]);
    }
}
