<template>
    <div>
        <template v-if="isLoading">
            <div class="nip-active-card-border p-4 nip-text-color">
                <div class="p-3">
                    <p class="fw-semibold fs-4 card-title"> {{ title }} </p>
                    <div class=" text-center p-4">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template v-else>
            <template v-if="!isCurrentSubStep">
                <div class="nip-inactive-card-border p-4">
                    <p class="fw-semibold nip-pending-card-title mb-0 p-3">
                        {{ title }}
                    </p>
                </div>
            </template>
            <template v-else>
                <div class="nip-active-card-border p-4 nip-text-color">
                    <div class="p-3">
                        <p class="fw-semibold fs-2 card-title nip-card-title"> {{ title }} </p>
                        <p class="mt-3 mb-3">You have chosen these business partners for your company. You’ll not only benefit from their expertise but also establish valuable connections with our network of trusted partners.</p>

                        <template v-if="leads && leads.length>0" class="" id="">
                            <div v-for="(lead, index) in leads" :key="index">
                                <div class="row mt-3 col-12" style="min-height: 40px">
                                    <div class="col-sm-3 d-flex align-items-center">
                                        <p class="fw-semibold mb-0">{{ lead.category.name }}</p>
                                    </div>
                                    <div class="col-sm-9 d-flex align-items-center justify-content-start">
                                        <img v-if="lead.offer.logo" class="nip-partner-logo"
                                             :src="lead.offer.logo" :alt="lead.offer.name">
                                    </div>
                                </div>
                            </div>
                        </template>
                        <hr class="col-12 mt-4 mb-4">
                        <template>
                            <form @submit.prevent="saveData" id="partner_information_form">
                                <div v-for="(informationSet, index) in informationSetCollection" :key="index">
                                    <div class="row">
                                        <div class="col-sm-9">
                                            <template v-if="informationSet.key === 'directorPrefill'">
                                                <p class="fw-semibold fs-5">Personal information</p>
                                                <div class="row mb-3">
                                                    <div class="col-sm-5">
                                                        <label class="fw-semibold" for="personal_info_director">
                                                            Please select the applying director:
                                                        </label>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <select id="personal_info_director"
                                                            class="form-select"
                                                            v-model="selectedDirector"
                                                        >
                                                            <option v-for="([id, director], index) in directorList"
                                                                :key="id"
                                                                :value="director"
                                                            >
                                                                {{ director.firstName ?? '' }} {{ director.lastName ?? '' }}
                                                            </option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </template>

                                            <template v-if="informationSet.key === 'personalEntityInformation'">
                                                <div class="row mb-3">
                                                    <div class="col-sm-5">
                                                        <label class="fw-semibold form-label" for="partner_info_name">Name *</label>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <input type="text" class="form-control" name="" id="partner_info_name" disabled :value="directorFullName" required>
                                                    </div>
                                                </div>
                                            </template>

                                            <template v-if="informationSet.key === 'email'">
                                                <div class="row mb-3">
                                                    <div class="col-sm-5">
                                                        <label class="fw-semibold form-label" for="partner_info_email">Email *</label>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <input type="email" class="form-control" v-model="prefillData.customer.email" id="partner_info_email" required>
                                                    </div>
                                                </div>
                                            </template>

                                            <template v-if="informationSet.key === 'dateOfBirth' || informationSet.key === 'aboveEighteenDateOfBirth'">
                                                <div class="row mb-3">
                                                    <label class="col-sm-5 form-label fw-semibold">Date of birth *</label>
                                                    <div class="col-sm-6 d-flex">
                                                        <input type="text" id="partner_info_day" class="form-control d-inline me-2" v-model="selectedDirector.dobDay" maxlength="2" disabled/>
                                                        <input type="text" id="partner_info_month" class="form-control d-inline me-2" v-model="selectedDirector.dobMonth" maxlength="2" disabled/>
                                                        <input type="text" id="partner_info_year" class="form-control d-inline" v-model="selectedDirector.dobYear" maxlength="4" disabled/>
                                                    </div>
                                                </div>
                                            </template>

                                        <template v-if="informationSet.key === 'phoneNumber'">
                                            <CountryPhoneCodeSelector
                                                :phoneNumber="prefillData?.customer.phone"
                                                @update:phoneNumber="updatePhoneNumber"
                                                :phone-label="hasBarclaysLead ? 'Phone number *' : 'Mobile phone number *'"
                                                :error-message="errors?.phone"
                                                input-id="phoneNumber_phone"
                                                :has-barclays-lead="hasBarclaysLead"
                                                :is-required="true"
                                            />
                                            <div class="row mb-3">
                                              <div class="offset-sm-5 col-sm-6 mt-1 mb-2 d-flex justify-content-start align-items-center gap-2" v-if="showPhoneNumberSuggestion">
                                                Suggestion:
                                                <span class="py-1 px-2 border rounded" role="button" @click="usePhoneNumberSuggestion">
                                                    {{phoneNumberSuggestion}}
                                                </span>
                                              </div>
                                            </div>
                                        </template>

                                        <template v-if="informationSet.key === 'alternativePhoneNumber'">
                                            <CountryPhoneCodeSelector
                                                @update:phoneNumber="updateAlternativePhoneNumber"
                                                v-model:phoneNumber="alternativePhoneNumber"
                                                phone-label="Alternative Phone Number"
                                                :error-message="errors?.alternativePhone"
                                                input-id="alternativePhoneNumber_phone"
                                                :is-required="false"
                                            />
                                        </template>

                                        <template v-if="informationSet.key === 'phoneNumberNonUk'">
                                            <CountryPhoneCodeSelector
                                                v-model:phoneNumber="phoneNumberNonUk"
                                                @update:phoneNumber="updatePhoneNumberNonUk"
                                                :error-message="errors?.phoneNonUk"
                                                phone-label="International Phone Number"
                                                :is-international="true"
                                                input-id="internationalPhoneNumber_phone"
                                                :is-required="true"
                                            />
                                        </template>

                                            <template v-if="informationSet.key === 'address'">
                                                <div class="row mb-3">
                                                    <div class="col-sm-5">
                                                        <label class="fw-semibold form-label" for="partner_info_address">Address *</label>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <input type="text" class="form-control" name="" id="partner_info_address" disabled :value="directorAddress" required>
                                                    </div>
                                                </div>
                                            </template>

                                            <template v-if="informationSet.key === 'barclaysCustomQuestions'">
                                                <BarclaysCustomQuestions
                                                    @update:barclaysQuestions="updateBarclaysQuestions"
                                                    :data="barclaysQuestions"
                                                />
                                            </template>

                                            <template v-if="informationSet.key === 'cashplusCustomQuestions'">
                                                <CashPlusCustomQuestions
                                                    @update:cashPlusQuestions="updateCashPlusQuestions"
                                                    :data="cashPlusQuestions"
                                                    :errors="errors"
                                                />
                                            </template>

                                            <template v-if="informationSet.key === 'preferredContactTime'">
                                                <div class="row mb-3">
                                                    <div class="col-sm-5">
                                                        <label class="fw-semibold form-label" for="partner_info_name_on_card">Preferred Contact Time *</label>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <select id="partner_info_preferred_contact_time"
                                                                v-model="preferredContactTime"
                                                                required="required"
                                                                class="form-control">
                                                            <option value="" selected="selected">Choose an option</option>
                                                            <option value="morning">morning</option>
                                                            <option value="afternoon">afternoon</option>
                                                            <option value="evening">evening</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </template>

                                            <template v-if="informationSet.key === 'nameOnCard'">
                                                <div class="row mb-3">
                                                    <div class="col-sm-5">
                                                        <label class="fw-semibold form-label" for="partner_info_name_on_card">Name on Card *</label>
                                                    </div>
                                                    <div class="col-sm-6">
                                                        <input type="text" id="partner_info_name_on_card" class="form-control" v-model="nameOnCard" required>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input nip-checkbox"
                                        type="checkbox"
                                        id="consentCheckbox"
                                        v-model="isConsentGiven"
                                        required
                                    />
                                    <label class="form-check-label ms-3 mt-1" for="consentCheckbox">
                                        I consent to my details and those of my company being shared with the partners
                                        I have selected  for the purpose of establishing and providing these services,
                                        as well as for direct contact by the partners to fulfill service requests. <br />
                                        I confirm that I have the authority to consent on behalf of those who benefit from the company being formed,
                                        allowing their details and those of the company to be shared with our partners for the purpose of establishing and providing these services.
                                        I also consent to being contacted directly by our partners to fulfill these service requests.
                                    </label>

                                </div>
                                <div class="alert alert-danger" v-if="errorMessage">
                                    {{ errorMessage }}
                                </div>
                                <button class="btn nip-btn-orange nip-next-step-button-larger d-flex align-items-center mb-0 mt-1 ms-auto me-3"
                                        type="submit"
                                        id="partner_info_continue_button_11735864"
                                >
                                    Submit & Continue
                                    <span class="arrow">
                                            <span class="arrow-shaft arrow-shaft-color-override-white"></span>
                                            <span class="arrow-head arrow-head-color-override-white"></span>
                                        </span>
                                </button>

                            </form>
                        </template>
                    </div>
                </div>
            </template>
        </template>
    </div>
</template>

<script lang="ts">
    import { defineComponent, ref } from 'vue';
    import api from "@/modules/CompanyIncorporation/Api"
    import CashPlusCustomQuestions from "@/modules/CompanyIncorporation/components/BusinessServicesStep/blocks/CashPlusCustomQuestions.vue"
    import BarclaysCustomQuestions from "@/modules/CompanyIncorporation/components/BusinessServicesStep/blocks/BarclaysCustomQuestions.vue"
    import CountryPhoneCodeSelector from "@/modules/CompanyIncorporation/components/global/CountryPhoneCodeSelector.vue"
    import {
        EVENT_KEY_APPOINTMENTS,
        EVENT_KEY_BUSINESS_SERVICES
    } from "@/modules/CompanyIncorporation/constants/eventKeys";
    import {countryPhoneCodeList} from "@/modules/CompanyIncorporation/constants/countryPhoneCodeList";
    import {scrollToSubStep} from "@/modules/CompanyIncorporation/helpers/subStepHelper";
    import {createLog} from "@/modules/CompanyIncorporation/listeners/logEventListener";
    import {
        COMPANY_INCORPORATION_BUSINESS_SERVICES,
        PARTNER_INFORMATION_EDIT,
        PARTNER_INFORMATION_LOAD,
        PARTNER_INFORMATION_SUBMIT
    } from "@/modules/CompanyIncorporation/constants/stepEnums";

    interface Lead {
        category: {
            name: String
        },
        offer: {
            name: String
        }
    }

    export default defineComponent ({
        name: 'PartnerInformation',
        components: {
            CashPlusCustomQuestions,
            BarclaysCustomQuestions,
            CountryPhoneCodeSelector
        },
        data() {
            return {
                title: 'Summary of Chosen Partner Services',
                isLoading: false,
                phoneNumberSuggestion: '',
                prefillData: {
                    customer: {
                        phone: ''
                    }
                },
                informationSetCollection: [],
                leads: [],
                selectedDirector: null,
                directorList: null as [],
                isConsentGiven: false,
                currentStep: 'partner Information',
                barclaysQuestions: {
                    existingClient: false,
                    pinSentryAccess: false,
                    onlineSignup: false
                },
                cashPlusQuestions: {
                    businessNameOnCard: '',
                    numberOfEmployees : null
                },
                preferredContactTime: null,
                nameOnCard: null,
                phoneNumberNonUk: '',
                alternativePhoneNumber: '',
                errors: {},
                errorMessage: '',
                isTriggeredFromBusiness: false
            }
        },
        props: {
            companyId: {
                type: Number,
                required: true
            },
            nextStep: {
                type: Object,
                required: true
            },
            subStep: {
                type: String,
                required: true
            },
            serializedCurrentStep: {
                type: Array,
                required: true
            }
        },
        computed: {
            directorAddress(): string {
                if (!this.selectedDirector) {
                    return '';
                }
                return `${this.selectedDirector.premise} ${this.selectedDirector.street} ${this.selectedDirector.postTown} ${this.selectedDirector.postcode} ${this.selectedDirector.country}`
            },
            directorFullName(): string {
                if (!this.selectedDirector) {
                    return '';
                }
                return `${this.selectedDirector.firstName} ${this.selectedDirector.lastName}`
            },
            isCurrentSubStep(): boolean {
                return this.currentSubStep === this.currentStep
            },
            currentSubStep(): string {
                return this.subStep
            },
            hasLeads(): boolean {
                return this.leads.length > 0
            },
            showPhoneNumberSuggestion(): boolean {
                return this.phoneNumberSuggestion && this.prefillData?.customer?.phone !== this.phoneNumberSuggestion
            },
            hasBarclaysLead(): boolean {
                for (let informationSet of this.informationSetCollection) {
                    if (informationSet.key === 'barclaysCustomQuestions') {
                        return true;
                    }
                }

                return false;
            }
        }, 
        methods: {
            async fetchData() {
                let isRedirecting = false
                this.isLoading = true;
                return api.get(`/api/incorporation/business_services/build_additional_information/${this.companyId}/`)
                .then(async (response) => {
                    const data = response.data
                    this.leads = data.leads
                    if (this.isTriggeredFromBusiness && this.leads.length === 0 ) {
                        isRedirecting = true
                        return this.redirectToNextStep();
                    }

                    this.prefillData = data.prefillData

                    if (this.prefillData.customer?.phone) {
                        this.phoneNumberSuggestion = this.prefillData.customer?.phone || ""
                        this.prefillData.customer?.phone = ""
                    }

                    this.informationSetCollection = data.informationSetCollection
                    this.setDirector()
                })
                .finally(() => {
                    if (!isRedirecting) {
                        this.isLoading = false;
                    }
                });
            },
            updatePhoneNumberNonUk(data: string) {
                if (data && !data.startsWith('+')) {
                    data = `+${data}`
                }

                this.phoneNumberNonUk = data
            },
            usePhoneNumberSuggestion() {
                this.prefillData.customer.phone = this.phoneNumberSuggestion;
            },
            updatePhoneNumber(data: string) {
                if (data && !data.startsWith('+')) {
                    data = `+${data}`
                }

                this.prefillData.customer.phone = data
            },
            updateAlternativePhoneNumber(data: string) {
                if (data && !data.startsWith('+')) {
                    data = `+${data}`
                }

                this.alternativePhoneNumber = data
            },
            additionalData() {
                this.errors = {}

                let type = 'mobile';

                if (this.hasBarclaysLead) {
                    type = 'regular';
                } else if (!this.prefillData?.customer?.phone.startsWith('+44')) {
                    type = 'international';
                }

                if (
                    this.prefillData?.customer?.phone
                    && !this.isPhoneValid(this.prefillData?.customer?.phone, type)
                ) {
                    this.errors.phone = this.hasBarclaysLead ? 'Please provide a valid UK phone number' : 'Please provide valid mobile phone number'
                    throw new Error('Please provide valid mobile phone number');
                }

                if (this.alternativePhoneNumber && this.alternativePhoneNumber.length <= 4) {
                    this.alternativePhoneNumber = null
                }
                if (
                    this.alternativePhoneNumber
                    && !this.isPhoneValid(
                        this.alternativePhoneNumber,
                        this.alternativePhoneNumber.startsWith('+44') ? 'mobile' : 'international'
                    )
                ) {
                    this.errors.alternativePhone = 'Please provide valid phone number'
                    throw new Error('Please provide valid phone number');
                }

                if (this.phoneNumberNonUk && this.phoneNumberNonUk.length <= 4) {
                    this.phoneNumberNonUk = null
                }
                if (
                    this.phoneNumberNonUk
                    && !this.isPhoneValid(
                        this.phoneNumberNonUk,
                        this.phoneNumberNonUk.startsWith('+44') ? 'mobile' : 'international'
                    )
                ) {
                    this.errors.phoneNumberNonUk = 'Please provide a valid international phone number'
                    throw new Error('Please provide a valid international phone number');
                }

                if (this.informationSetCollection.some((item => item.key === 'cashplusCustomQuestions'))) {
                    if (this.cashPlusQuestions.numberOfEmployees < 1) {
                        this.errors.cashplusNumberOfEmployees = 'Number of employees must be more than 0';
                        throw new Error('Number of employees must be more than 0');
                    }
                }

                return {
                    personalEntity: {
                        title: this.selectedDirector.title,
                        firstName: this.selectedDirector.firstName,
                        middleName: this.selectedDirector.middleName,
                        lastName: this.selectedDirector.lastName,
                    },
                    dateOfBirth: this.selectedDirector.dob,
                    address: {
                        premise: this.selectedDirector.premise,
                        street: this.selectedDirector.street,
                        thoroughfare: this.selectedDirector.thoroughfare,
                        postcode: this.selectedDirector.postcode,
                        postTown: this.selectedDirector.postTown,
                        county: this.selectedDirector.county,
                        country: this.selectedDirector.country,
                    },
                    email: this.prefillData.customer.email,
                    phoneNumber: this.prefillData.customer.phone,
                    phoneNumberNonUk: this.phoneNumberNonUk,
                    consent: this.isConsentGiven,
                    directors: this.prefillData.directors,
                    directorPrefill: this.prefillData.directors,
                    barclaysCustomQuestions: this.barclaysQuestions,
                    cashPlusCustomQuestions: this.cashPlusQuestions,
                    alternativePhoneNumber: this.alternativePhoneNumber,
                    nameOnCard: this.nameOnCard,
                    preferredContactTime: this.preferredContactTime
                }
            },
            updateCashPlusQuestions(data: any) {
                this.cashPlusQuestions.numberOfEmployees = data.numberOfEmployees;
                this.cashPlusQuestions.businessNameOnCard = data.businessNameOnCard;
            },
            isPhoneValid(phone: string, type: string): boolean {
                switch (type) {
                    case 'mobile':
                        const regexMobilePhone = /^\+447[0-9]{8,9}$/;
                        return regexMobilePhone.test(phone);

                    case 'international':
                        let regexInternationalPhone = /^\+(?:[0-9]?)\d{3,14}$/;
                        if (phone.length > 14) {
                            for (const country of countryPhoneCodeList) {
                                if (country.code.length < 4) continue;
                                if (phone.startsWith(country.code)) {
                                    regexInternationalPhone = /^\+(?:[0-9]?)\d{1,16}$/;
                                    break;
                                }
                            }
                        }
                        return regexInternationalPhone.test(phone.replace(/\s/g, ''));

                    default:
                        const normalized = phone.replace(/[\s-]/g, '');
                        const regexUKPhone = /^(?:(?:\+44|0044)0?|0)\d{10}$/;
                        return regexUKPhone.test(normalized);
                }
            },
            updateBarclaysQuestions(data: any) {
                this.barclaysQuestions.existingClient = data.existingClient;
                this.barclaysQuestions.pinSentryAccess = data.pinSentryAccess;
                this.barclaysQuestions.onlineSignup = data.onlineSignup;
            },
            setDirector() {
                this.directorList = Object.entries(this.prefillData.directors);
                this.selectedDirector = ref(this.directorList[0][1]);
            },
            async saveData() {
                this.isLoading = true;
                this.errorMessage = '';
                let additionalData;

                try {
                    additionalData = this.additionalData();
                } catch (e) {
                    this.isLoading = false;
                    return;
                }

                await createLog(this.companyId, {
                    newIncorporationProcess: true,
                    step: COMPANY_INCORPORATION_BUSINESS_SERVICES,
                    subStep: PARTNER_INFORMATION_SUBMIT,
                    data: JSON.stringify({...this.$data, ...this.$props}),
                });

                api.post(`/api/incorporation/business_services/process_additional_information/${this.companyId}/`,
                    {
                        leads: this.leads,
                        data: additionalData,
                    },
                    {
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    }
                )
                .then(() => {
                    this.redirectToNextStep()
                })
                .catch(error => {
                    if (error?.response?.data?.message) {
                        this.errors = error.response.data.message;
                        return;
                    }

                    if (error?.response?.data?.error) {
                        this.errors = error.response.data.error;
                        return;
                    }

                    this.errors = error.message;
                    this.errorMessage = error.message;
                })
                .finally(() => {
                    this.isLoading = false;
                })
            },
            stepEditingListener() {
                document.addEventListener('update-partner-info-data', async (event: CustomEvent) => {
                    await this.fetchData();

                    await createLog(this.companyId, {
                        newIncorporationProcess: true,
                        step: COMPANY_INCORPORATION_BUSINESS_SERVICES,
                        subStep: PARTNER_INFORMATION_EDIT,
                        data: JSON.stringify({...this.$data, ...this.$props}),
                    });
                })
            },
            triggerFromBusinessPartnersListener() {
                document.addEventListener('from-business-partners-step', (event: CustomEvent) => {
                    if (event.detail?.triggerAction) {
                        this.isTriggeredFromBusiness = event.detail.triggerAction
                    }
                })
            },
            redirectToNextStep() {
                return api.post(`/api/incorporation/incorporation_events/log_step_filled_event/${this.companyId}/`, {
                    eventKey: EVENT_KEY_BUSINESS_SERVICES,
                    nextStepKey: this.serializedCurrentStep[2].stepKey,
                }).then(res => {
                    if (res.status !== 200) {
                        this.errorMessage = res.data.message;
                        return;
                    }

                    window.location.href = res.data.nextStepUrl;
                })
                .catch(error => {
                    if (error.response && error.response.data && error.response.data.message) {
                        this.errors = error.response.data.message;
                        return;
                    }

                    this.errors = error.message;
                })
            },
        },
        watch: {
            'prefillData.company.name': {
                handler(newVal) {
                    if (newVal) {
                        this.cashPlusQuestions.businessNameOnCard = newVal;
                    }
                },
                immediate: true,
            },
            "subStep": {
                handler() {
                    if (this.isCurrentSubStep) {
                        scrollToSubStep('business-service-partner-info');
                    }
                },
                immediate: true
            }
        },
        async mounted() {
            this.triggerFromBusinessPartnersListener()
            await this.fetchData();
            this.stepEditingListener();

            try {
                this.updatePhoneNumber(this.prefillData.customer.phone);
            } catch {}

            try {
                this.updatePhoneNumberNonUk(this.phoneNumberNonUk);
            } catch {}

            try {
                this.updateAlternativePhoneNumber(this.alternativePhoneNumber);
            } catch {}

            await createLog(this.companyId, {
                newIncorporationProcess: true,
                step: COMPANY_INCORPORATION_BUSINESS_SERVICES,
                subStep: PARTNER_INFORMATION_LOAD,
                data: JSON.stringify({...this.$data, ...this.$props}),
            });
        },
        beforeUnmount() {
            document.removeEventListener('update-partner-info-data', () => {});
            document.removeEventListener('from-business-partners-step', () => {});
        }
    })
</script>

<style scoped>
.w10 {
    width: 85px;
}
</style>