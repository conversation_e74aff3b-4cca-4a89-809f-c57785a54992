import $ from 'jquery';

export class Tabs {
  constructor (selector, tabs, active) {
    this.selector = selector;
    this.tabs = tabs;
    this.active = active;
  }

  static fromDocumentType (selector, documentType) {
    return new Tabs(
      selector,
      {
        PASSPORT: 0,
        INTERNATIONAL_PASSPORT: 0,
        LICENSE: 1,
        EUROPEAN_CARD: 2
      },
      documentType.value
    );
  }

  init (activate) {
    $(this.selector).tabs(
      {
        active: this.getTabIndex(this.active),
        activate: activate
      }
    );
  }

  activateTab (index) {
    $(this.selector).tabs('option', 'active', index);
  }

  getTabIndex (type) {
    return this.tabs[type];
  }

  isPassportTabActive () {
    return (this.active === 'PASSPORT');
  }
}
