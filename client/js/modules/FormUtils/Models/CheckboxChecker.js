export class CheckboxChecker {
  constructor (defaultData, selectedOffersBuffer) {
    this.checked = defaultData || {};
    this.useObjectContext = true;

    this.selectedOffersBuffer = selectedOffersBuffer;

    for (const key in defaultData) {
      this.selectedOffersBuffer.toggleOffer(key);
    }
  }

  checkNew (event, element) {
    event.preventDefault();
    const checkValue = element.getAttribute('data-value');
    this.checked[checkValue] = checkValue;
  }

  toggleChecked (event, element) {
    event.preventDefault();
    const toggleValue = element.getAttribute('data-value');
    this.checked[toggleValue] = !this.checked[toggleValue] ? toggleValue : null;
    this.selectedOffersBuffer.toggleOffer(toggleValue);
  }
}
