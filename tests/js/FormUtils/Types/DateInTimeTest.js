import { DateInTime } from '../../../../client/js/modules/FormUtils/Types/DateInTime';
import * as assert from 'assert';
import { given } from 'mocha-testdata';

describe('DateInTime', function () {
  describe('isValid', function () {
    const data = [
      ['1999', '02', '01', true, '01/02/1999'],
      ['2204', '2', '1', true, '01/02/2204'],
      ['2018', '02', '1', true, '01/02/2018'],
      ['1999', '2', '01', true, '01/02/1999'],
      ['1800', '02', '01', true, '01/02/1800'],
      ['a', '02', '01', false, null],
      ['1999', '2b', '1', false, null],
      ['199b', '2', '1', false, null],
      ['1999', '0', '1', false, null],
      ['1999', '0', '1', false, null],
      ['1999', '02', '29', false, null]
    ];
    given(data).it('it should validate date', function (year, month, day, valid) {
      var date = new DateInTime(year, month, day);
      assert.strictEqual(date.isValid(), valid);
    });

    given(data).it('it should convert to string', function (year, month, day, valid, expected) {
      var date = new DateInTime(year, month, day);
      assert.strictEqual(date.toString(), expected);
    });

    const namedData = [
      ['01/02/1999', 'DD/MM/YYYY', '01/02/1999', null],
      ['1999-02-1', 'YYYY-MM-D', '01/02/1999'],
      ['1999-02-1', 'YYYY-MM-D', '1999/02/01', 'YYYY/MM/DD'],
      ['a', 'YYYY-MM-DD', null, null]
    ];

    given(namedData).it('it should be created from string', function (fromDate, fromFormat, toDate, toFormat) {
      var date = fromFormat ? DateInTime.fromString(fromDate, fromFormat) : DateInTime.fromString(fromDate);
      assert.strictEqual(toFormat ? date.toString(toFormat) : date.toString(), toDate);
    });
  });
});
