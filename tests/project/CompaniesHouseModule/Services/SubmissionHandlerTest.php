<?php

namespace tests\project\CompaniesHouseModule\Services;

use CompaniesHouseModule\Services\SubmissionHandler;
use Libs\CHFiling\Core\Company as CompanyOld;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission as FormSubmissionEntity;
use Entities\Customer;
use Libs\CHFiling\Core\Request\FormSubmission;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use TestModule\PhpUnit\TestCase;

class SubmissionHandlerTest extends TestCase
{
    /**
     * @var SubmissionHandler
     */
    private $object;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var FormSubmission[]
     */
    private $submissions;

    /**
     * @var array
     */
    private $customers;

    /**
     * @var array
     */
    private $companies;

    /**
     * @Inject({"object"="companies_house_module.services.submission_handler", "databaseHelper"="test_module.helpers.database_helper"})
     */
    public function setUpDependencies(SubmissionHandler $object, DatabaseHelper $databaseHelper)
    {
        $this->object = $object;
        $this->databaseHelper = $databaseHelper;
        $this->databaseHelper->emptyTables([TBL_CUSTOMERS, TBL_COMPANIES, TBL_COMPANY_INCORPORATIONS, TBL_OFFICER_RESIGNATIONS, TBL_OFFICER_APPOINTMENTS, TBL_FORM_SUBMISSIONS]);
        $this->customers = [
            Customer::temporary('test1'),
            Customer::temporary('test2'),
        ];
        $this->companies = [
            new Company($this->customers[0], 'test1'),
            new Company($this->customers[0], 'test2'),
            new Company($this->customers[1], 'test3'),
            new Company($this->customers[1], 'test4')
        ];
        $this->databaseHelper->saveEntities(array_merge($this->customers, $this->companies));
        $this->submissions = [
            FormSubmission::getNewFormSubmission(CompanyOld::getCompany($this->companies[0]->getId()), 'CompanyIncorporation'),
            FormSubmission::getNewFormSubmission(CompanyOld::getCompany($this->companies[1]->getId()), 'OfficerResignation'),
            FormSubmission::getNewFormSubmission(CompanyOld::getCompany($this->companies[1]->getId()), 'OfficerAppointment'),
            FormSubmission::getNewFormSubmission(CompanyOld::getCompany($this->companies[2]->getId()), 'OfficerAppointment'),
            FormSubmission::getNewFormSubmission(CompanyOld::getCompany($this->companies[3]->getId()), 'OfficerAppointment'),
        ];
        $this->submissions[0]->setResponse(FormSubmissionEntity::RESPONSE_PENDING);
        $this->submissions[1]->setResponse(FormSubmissionEntity::RESPONSE_PENDING);
        $this->submissions[2]->setResponse(FormSubmissionEntity::RESPONSE_PENDING);
        $this->submissions[3]->setResponse(FormSubmissionEntity::RESPONSE_ACCEPT);
        $this->submissions[4]->nullifyResponse();
    }

    public function testChangeSubmissionsToDeleted()
    {
        $result = $this->object->changePendingSubmissionsToDeleted($this->companies[0]);
        $this->assertEquals(1, $result);
        $result = $this->object->changePendingSubmissionsToDeleted($this->companies[1]);
        $this->assertEquals(2, $result);
        $result = $this->object->changePendingSubmissionsToDeleted($this->companies[2]);
        $this->assertEquals(0, $result);
        $result = $this->object->changePendingSubmissionsToDeleted($this->companies[3]);
        $this->assertEquals(0, $result);
    }

    public function testChangeToDeleted()
    {
        $formSubmission = $this->object->changeSubmissionToDeleted($this->companies[0], $this->submissions[0]->getFormSubmissionId());
        $this->assertTrue($formSubmission->isDeleted());
        $formSubmission = FormSubmission::getFormSubmission(CompanyOld::getCompany($this->companies[0]->getId()), $this->submissions[0]->getFormSubmissionId());
        $this->assertTrue($formSubmission->isDeleted());
    }

    public function testWithHoldSubmissionOnlyOnce()
    {
        $item1 = $this->object->withHoldSubmission($this->companies[0], $this->submissions[0]->getFormSubmissionId());
        $item2 = $this->object->withHoldSubmission($this->companies[0], $this->submissions[0]->getFormSubmissionId());
        $this->assertEquals($item1, $item2);
    }
}
