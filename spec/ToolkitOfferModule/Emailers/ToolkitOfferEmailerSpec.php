<?php

namespace spec\ToolkitOfferModule\Emailers;

use CompanyFormationModule\Repositories\SicCodesRepository;
use EmailModule\IEmail;
use EmailModule\IEmailGateway;
use EmailModule\IEmailLog;
use Entities\Company;
use Entities\Customer;
use Framework\FEmail;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use ToolkitOfferModule\Emailers\EmailFactory;
use ToolkitOfferModule\Emailers\ToolkitOfferEmailer;
use ToolkitOfferModule\Entities\CompanyToolkitOffer;
use ToolkitOfferModule\Entities\ToolkitOffer;
use ToolkitOfferModule\Helpers\EmailPlaceholderHelper;

/**
 * @mixin ToolkitOfferEmailer
 */
class ToolkitOfferEmailerSpec extends ObjectBehavior
{
    /**
     * @var EmailFactory
     */
    private $emailFactory;

    /**
     * @var IEmailGateway
     */
    private $emailGateway;

    /**
     * @var EmailPlaceholderHelper
     */
    private $placeholderHelper;

    /**
     * @var SicCodesRepository
     */
    private $sicCodesRepository;

    function let(
        IEmailGateway $emailGateway,
        EmailFactory $emailFactory,
        EmailPlaceholderHelper $placeholderHelper,
        SicCodesRepository $sicCodesRepository
    )
    {
        $this->emailGateway = $emailGateway;
        $this->emailFactory = $emailFactory;
        $this->placeholderHelper = $placeholderHelper;
        $this->sicCodesRepository = $sicCodesRepository;
        $this->beConstructedWith(
            $this->emailGateway,
            $this->emailFactory,
            $this->placeholderHelper,
            $this->sicCodesRepository
        );
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(ToolkitOfferEmailer::class);
    }

    function it_can_send_tax_assist_email(Customer $customer, FEmail $email)
    {
        $customer->getEmail()->willReturn('<EMAIL>');
        $customer->getFirstName()->willReturn('FirstName');

        $this->emailFactory->getTaxAssistEmail($customer)->willReturn($email);

        $this->sendTaxAssistEmail($customer);

        $this->emailGateway->send($email, $customer)->shouldBeCalled();
    }

    function it_can_send_free_agent_email(Customer $customer, FEmail $email)
    {
        $this->emailFactory->getFreeAgentEmail()->willReturn($email);
        $customer->getEmail()->willReturn('<EMAIL>');
        $customer->getFirstName()->willReturn('FirstName');

        $this->sendFreeAgentEmail($customer);

        $this->emailGateway->send($email, $customer)->shouldBeCalled();
    }

    function it_can_send_how_to_make_a_profit_ebook_email(Customer $customer, FEmail $email)
    {
        $this->emailFactory->getEbookEmail($customer)->willReturn($email);
        $customer->getEmail()->willReturn('<EMAIL>');
        $customer->getFirstName()->willReturn('FirstName');

        $this->sendMakeMoreProfitGuideEmail($customer);

        $this->emailGateway->send($email, $customer)->shouldBeCalled();
    }

    function it_can_send_facebook_group_invite_email(Customer $customer, FEmail $email)
    {
        $this->emailFactory->getFacebookGroupEmail($customer)->willReturn($email);
        $customer->getEmail()->willReturn('<EMAIL>');
        $customer->getFirstName()->willReturn('FirstName');

        $this->sendFacebookGroupEmail($customer);

        $this->emailGateway->send($email, $customer)->shouldBeCalled();
    }

    function it_can_send_offer_email(IEmail $email)
    {
        $this->emailFactory->getEmailByType(ToolkitOffer::TYPE_BROOKSON, Argument::any())->willReturn($email);
        $this->emailFactory->getLeadEmailByType(ToolkitOffer::TYPE_BROOKSON, Argument::any())->willReturn($email);
        $customer = Customer::temporary('<EMAIL>');
        $company = new Company($customer, 'test');
        $offer = new ToolkitOffer();
        $offer->setType(ToolkitOffer::TYPE_BROOKSON);
        $companyOffer = new CompanyToolkitOffer($company, $offer);
        $this->placeholderHelper->getAll($company, $customer)->willReturn([]);
        $this->sendEmailOffer($companyOffer);
    }
}
