<?php

namespace spec\AdminModule\Deciders;

use AdminModule\Deciders\CompanyTransferDecider;
use AdminModule\Deciders\ICompanyTransferDecider;
use Entities\Customer;
use IdModule\Verification\IIdValidationChecker;
use PhpSpec\Exception\Example\SkippingException;
use PhpSpec\ObjectBehavior;

class CompanyTransferDeciderSpec extends ObjectBehavior
{
    /**
     * @var IIdValidationChecker
     */
    private $validationChecker;

    public function let(IIdValidationChecker $validationChecker)
    {
        $this->validationChecker = $validationChecker;

        $this->beConstructedWith($validationChecker);
    }
    function it_is_initializable()
    {
        $this->shouldHaveType(CompanyTransferDecider::class);
    }

    function it_should_return_decisions_for_customer_with_new_id_check_validated(Customer $fromCustomer, Customer $toCustomer1, Customer $toCustomer2, Customer $toCustomer3, Customer $toCustomer4, Customer $toCustomer5)
    {
        throw new SkippingException('Testing the behavior, will be refactored');
        //new validated
        $this->validationChecker->isCustomerValid($fromCustomer)->willReturn(TRUE);

        $this->mockCustomers($toCustomer1, $toCustomer2, $toCustomer3, $toCustomer4, $toCustomer5);

        $this->getDecision($fromCustomer, $toCustomer1)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
        $this->getDecision($fromCustomer, $toCustomer2)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
        $this->getDecision($fromCustomer, $toCustomer3)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
        $this->getDecision($fromCustomer, $toCustomer4)->shouldReturn(ICompanyTransferDecider::TRANSFER_WITH_WARNING);
    }

    function it_should_return_decisions_for_customer_with_new_id_check_required(Customer $fromCustomer, Customer $toCustomer1, Customer $toCustomer2, Customer $toCustomer3, Customer $toCustomer4, Customer $toCustomer5)
    {
        throw new SkippingException('Testing the behavior, will be refactored');
        //new required
        $this->validationChecker->isCustomerValid($fromCustomer)->willReturn(FALSE);

        $this->mockCustomers($toCustomer1, $toCustomer2, $toCustomer3, $toCustomer4, $toCustomer5);

        $this->getDecision($fromCustomer, $toCustomer1)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
        $this->getDecision($fromCustomer, $toCustomer2)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
        $this->getDecision($fromCustomer, $toCustomer3)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
        $this->getDecision($fromCustomer, $toCustomer4)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
    }

    function it_should_return_decisions_for_customer_with_id_check_not_required(Customer $fromCustomer, Customer $toCustomer1, Customer $toCustomer2, Customer $toCustomer3, Customer $toCustomer4, Customer $toCustomer5)
    {
        throw new SkippingException('Testing the behavior, will be refactored');
        //new required
        $this->validationChecker->isCustomerValid($fromCustomer)->willReturn(NULL);

        $this->mockCustomers($toCustomer1, $toCustomer2, $toCustomer3, $toCustomer4, $toCustomer5);

        $this->getDecision($fromCustomer, $toCustomer1)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
        $this->getDecision($fromCustomer, $toCustomer2)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
        $this->getDecision($fromCustomer, $toCustomer3)->shouldReturn(ICompanyTransferDecider::TRANSFER_NORMAL);
        $this->getDecision($fromCustomer, $toCustomer4)->shouldReturn(ICompanyTransferDecider::TRANSFER_WITH_WARNING);
    }

    private function mockCustomers(Customer $toCustomer1, Customer $toCustomer2, Customer $toCustomer3, Customer $toCustomer4, Customer $toCustomer5)
    {
        //old validated
        $this->validationChecker->isCustomerValid($toCustomer1)->willReturn(NULL);

        //new validated
        $this->validationChecker->isCustomerValid($toCustomer2)->willReturn(TRUE);

        //old required
        $this->validationChecker->isCustomerValid($toCustomer3)->willReturn(NULL);

        //new required
        $this->validationChecker->isCustomerValid($toCustomer4)->willReturn(FALSE);

        //not required
        $this->validationChecker->isCustomerValid($toCustomer5)->willReturn(NULL);
    }
}
