<?php

namespace spec\CompanySyncModule\Repositories;

use CompanySyncModule\Entities\CompaniesSync;
use CompanySyncModule\Repositories\CompaniesSyncRepository;
use PhpSpec\ObjectBehavior;
use Redis;
use SerializingModule\SerializerInterface;

class CompaniesSyncRepositorySpec extends ObjectBehavior
{
    /**
     * @var Redis
     */
    private $redis;

    /**
     * @var string
     */
    private $namespace;

    /**
     * @var SerializerInterface
     */
    private $serializer;


    function let(Redis $redis, SerializerInterface $serializer)
    {
        $this->redis = $redis;
        $this->namespace = 'cms';
        $this->serializer = $serializer;

        $this->beConstructedWith($this->redis, $this->namespace, $this->serializer);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(CompaniesSyncRepository::class);
    }

    function it_should_return_entity(CompaniesSync $companiesSync)
    {
        $customerId = 1;

        $data = 'data';
        $this->redis->get($this->getKey($customerId))->willReturn($data);
        $this->serializer->unserialize($data, CompaniesSync::class)->willReturn($companiesSync);

        $this->findById($customerId)->shouldBeAnInstanceOf(CompaniesSync::class);
    }

    function it_should_return_null()
    {
        $customerId = 1;

        $this->redis->get($this->getKey($customerId))->willReturn(NULL);

        $this->findById($customerId)->shouldBe(NULL);
    }

    function it_should_save_entity(CompaniesSync $entity)
    {
        $customerId = 1;
        $data = 'data';
        $timeout = 3600;

        $entity->getCustomerId()->willReturn($customerId);
        $this->serializer->serialize($entity)->willReturn($data);
        $this->redis->set($this->getKey($customerId), $data, $timeout)->shouldBeCalled();

        $this->save($entity, $timeout);
    }

    function it_remove_entity(CompaniesSync $entity)
    {
        $customerId = 1;
        $entity->getCustomerId()->willReturn($customerId);
        $this->redis->del($this->getKey($customerId))->shouldBeCalled();

        $this->remove($entity);
    }

    /**
     * @param int $customerId
     * @return string
     */
    private function getKey($customerId)
    {
        return sprintf('%s.sync_companies.%d', $this->namespace, $customerId);
    }


}
