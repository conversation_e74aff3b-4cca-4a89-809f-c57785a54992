<?php

namespace FormSubmissionModule\Services;

use Entities\Company;
use Entities\CompanyHouse\FormSubmission;

class FormSubmissionService
{
    public function isPendingForCompany(Company $company, string $formIdentifier): bool
    {
        $submissions = $company->getFormSubmissions()->filter(
            function (FormSubmission $submission) use ($formIdentifier) {
                return $submission->getFormIdentifier() === $formIdentifier;
            }
        );

        if ($submissions->isEmpty()) {
            return FALSE;
        }

        return $submissions->first()->isPending();
    }
}
