<?php

namespace ServiceRemindersModule\Providers;

use Entities\Company;
use Entities\Service;
use Models\View\Front\CompanyServicesView;
use ServiceRemindersModule\Checkers\CreditControlRequirementChecker;
use ServiceRemindersModule\Deciders\AddressReminderDecider;
use ServiceRemindersModule\Deciders\ServiceAddressTypeDecider;
use ServiceRemindersModule\Entities\Reminder;
use ServiceRemindersModule\Factories\ReminderServicesFactory;
use ServiceRemindersModule\Filters\AddressServicesFilter;
use ServiceRemindersModule\Filters\EventServicesFilter;
use Utils\Exceptions\InvalidArgumentException;
use Utils\Helpers\ArrayHelper;

class RemindersProvider
{
    /**
     * @var array
     */
    private $remindersData;

    /**
     * @var AddressServicesFilter
     */
    private $addressServicesFilter;

    /**
     * @var AddressReminderDecider
     */
    private $addressReminderDecider;

    /**
     * @var ReminderServicesFactory
     */
    private $reminderServicesFactory;

    /**
     * @var EventServicesFilter
     */
    private $eventServicesFilter;

    /**
     * @var CreditControlRequirementChecker
     */
    private $creditControlRequirementChecker;

    /**
     * @var ServiceAddressTypeDecider
     */
    private $serviceAddressTypeDecider;

    public function __construct(
        array $remindersData,
        AddressServicesFilter $addressServicesFilter,
        AddressReminderDecider $addressReminderDecider,
        ReminderServicesFactory $reminderServicesFactory,
        EventServicesFilter $eventServicesFilter,
        CreditControlRequirementChecker $creditControlRequirementChecker,
        ServiceAddressTypeDecider $serviceAddressTypeDecider
    ) {
        $this->remindersData = $remindersData;
        $this->addressServicesFilter = $addressServicesFilter;
        $this->addressReminderDecider = $addressReminderDecider;
        $this->reminderServicesFactory = $reminderServicesFactory;
        $this->eventServicesFilter = $eventServicesFilter;
        $this->creditControlRequirementChecker = $creditControlRequirementChecker;
        $this->serviceAddressTypeDecider = $serviceAddressTypeDecider;
    }

    /**
     * @param CompanyServicesView $companyServicesView
     * @return iterable|Reminder
     * @throws InvalidArgumentException
     */
    public function getReminders(CompanyServicesView $companyServicesView): iterable
    {
        foreach ($this->remindersData as $reminderData) {

            $days = ArrayHelper::get($reminderData, Reminder::KEY_DAYS);
            $event = ArrayHelper::get($reminderData, Reminder::KEY_EVENT);
            $isAddressRequired = ArrayHelper::get($reminderData, Reminder::KEY_ADDRESS_REQUIRED, FALSE);
            $isCreditControl = ArrayHelper::get($reminderData, Reminder::KEY_CREDIT_CONTROL, FALSE);
            $dependsOnEvent = ArrayHelper::get($reminderData, Reminder::KEY_DEPENDS_ON_EVENT, NULL);
            $keyComprehensiveTemplate = ArrayHelper::get($reminderData, Reminder::KEY_HAS_COMPREHENSIVE_PACKAGE, NULL);
            $keyAddress = ArrayHelper::get($reminderData, [Reminder::KEY_SUBJECT, Reminder::KEY_ADDRESS], NULL);
            $keyNoAddress = ArrayHelper::get($reminderData, [Reminder::KEY_SUBJECT, Reminder::KEY_NO_ADDRESS], NULL);

            if (!$companyServicesView->hasServicesWithoutEventExpiringIn($days, $event)) {
                continue;
            }

            $services = $this->reminderServicesFactory->createFromServiceViews(
                $companyServicesView->getServicesWithoutEventExpiringIn($days, $event)
            );

            // Check address requirement
            if ($isAddressRequired) {
                $services = $this->addressServicesFilter->filter($services);
            }

            // Check event requirement
            if ($dependsOnEvent && !empty($services)) {
                $services = $this->eventServicesFilter->filter($dependsOnEvent, $services);
            }

            if (empty($services)) {
                continue;
            }

            $company = $companyServicesView->getCompany();

            // Check credit control email requirement
            if ($isCreditControl && !$this->creditControlRequirementChecker->check($company)) {
                continue;
            }

            if ($keyComprehensiveTemplate && !$this->hasComprehensivePackage($services) && (!$keyAddress || !$keyNoAddress)) {
                continue;
            }

            yield $this->buildReminder(
                $company, $reminderData, $isAddressRequired, $event, $services
            );
        }
    }

    private function buildReminder(
        Company $company,
        array $reminderData,
        bool $isAddressRequired,
        string $event,
        array $services
    ): Reminder {

        if ($isAddressRequired) {
            $useAddressTemplatePreview = TRUE;
        } else {
            $useAddressTemplatePreview = $this->addressReminderDecider->isAddressReminder($services);
        }
        $keyComprehensiveTemplate = ArrayHelper::get($reminderData, Reminder::KEY_HAS_COMPREHENSIVE_PACKAGE, NULL) ===
            $this->hasComprehensivePackage($services);

        return new Reminder(
            ArrayHelper::get($reminderData, Reminder::KEY_KEY),
            ArrayHelper::get($reminderData, Reminder::KEY_EMAIL),
            $keyComprehensiveTemplate ? $this->getComprehensiveConditionParameter(Reminder::KEY_TEMPLATE, $reminderData) :
                $this->getAddressConditionParameter(Reminder::KEY_TEMPLATE, $useAddressTemplatePreview, $reminderData),
            $keyComprehensiveTemplate ? $this->getComprehensiveSubject($company->getCompanyName()) :
                $this->getAddressConditionParameter(Reminder::KEY_SUBJECT, $useAddressTemplatePreview, $reminderData),
            $keyComprehensiveTemplate ? $this->getComprehensiveConditionParameter(Reminder::KEY_PREVIEW, $reminderData) :
                $this->getAddressConditionParameter(Reminder::KEY_PREVIEW, $useAddressTemplatePreview, $reminderData),
            $event,
            $company,
            $services,
            $this->serviceAddressTypeDecider->getServiceAddressType($company, $services)
        );
    }

    private function getAddressConditionParameter(string $parameterKey, bool $addressType, array $reminderData): string
    {
        $templateData = ArrayHelper::get($reminderData, $parameterKey);
        return ArrayHelper::get(
            $templateData,
            $addressType ? Reminder::KEY_ADDRESS : Reminder::KEY_NO_ADDRESS
        );
    }

    private function getComprehensiveConditionParameter(string $parameterKey, array $reminderData): string
    {
        $templateData = ArrayHelper::get($reminderData, $parameterKey);
        return ArrayHelper::get(
            $templateData,
            Reminder::COMPREHENSIVE
        );
    }

    private function getComprehensiveSubject(string $companyName): string
    {
        return sprintf("Annual Comprehensive package Payment Reminder for %s", $companyName);
    }

    private function hasComprehensivePackage(array $services): bool
    {
        foreach ($services as $service){
            if ($service->getType() === Service::TYPE_PACKAGE_COMPREHENSIVE) {
                return true;
            }
        }

        return false;
    }
}
