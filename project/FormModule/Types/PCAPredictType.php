<?php

namespace FormModule\Types;

use FormModule\Dto\Address;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class PCAPredictType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add(
                'search',
                TextType::class,
                [
                    'mapped' => FALSE,
                    'required' => FALSE,
                    'attr' =>
                        [
                            'placeholder' => 'BBB Start typing a postcode, street or address',
                            'size' => 40,
                            'data-pca' => 'search-input'
                        ],
                ]
            );
    }

    public function getName()
    {
        return 'pca_predict';
    }
}