<?php

declare(strict_types=1);

namespace OrderModule\DataAccessors;

use Entities\Customer;
use OrderModule\Dto\Order;
use OrderModule\Dto\Orders;

interface OrderDataAccessorInterface
{
    public function create(Order $order): Order;
    public function findById(int $id): ?Order;
    public function findByCustomer(Customer $customer): ?Orders;

    public function getFailedOrdersPaidWithDirectDebit(): ?Orders;
}
