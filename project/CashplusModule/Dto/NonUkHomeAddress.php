<?php

namespace CashplusModule\Dto;

use CompaniesHouseModule\Deciders\CountryDecider;
use CompaniesHouseModule\Entities\IAddress;
use Models\ValueObject\Country;

class NonUkHomeAddress
{
    /**
     * @var string
     */
    private $homeAddress_AddressLine1;

    /**
     * @var string
     */
    private $homeAddress_AddressLine2;

    /**
     * @var string
     */
    private $homeAddress_AddressLine3;

    /**
     * @var string
     */
    private $homeAddress_AddressLine4;

    /**
     * @var string
     */
    private $homeAddress_CountryId;

    public static function fromPscAddress(IAddress $address): self
    {
        $obj = new self();
        $obj->setHomeAddressAddressLine1(!empty($address->getPostcode()) ? $address->getPostcode() : 'n/a');
        $obj->setHomeAddressAddressLine2(!empty($address->getPremise()) ? $address->getPremise() : 'n/a');
        $obj->setHomeAddressAddressLine3(!empty($address->getThoroughfare()) ? $address->getThoroughfare() : 'n/a');
        $obj->setHomeAddressAddressLine4(!empty($address->getStreet()) ? $address->getStreet() : 'n/a');

        if (!$country = $address->getCountry()) {
            $obj->setHomeAddressCountryId('na');
            return $obj;
        }

        if (CountryDecider::isCountryIso($country)) {
            $obj->setHomeAddressCountryId($country);
            return $obj;
        }

        $obj->setHomeAddressCountryId(Country::fromName($country)->getIso());
        return $obj;
    }

    public function getHomeAddressAddressLine1(): string
    {
        return $this->homeAddress_AddressLine1;
    }

    public function setHomeAddressAddressLine1(string $homeAddress_AddressLine1): void
    {
        $this->homeAddress_AddressLine1 = $homeAddress_AddressLine1;
    }

    public function getHomeAddressAddressLine2(): string
    {
        return $this->homeAddress_AddressLine2;
    }

    public function setHomeAddressAddressLine2(string $homeAddress_AddressLine2): void
    {
        $this->homeAddress_AddressLine2 = $homeAddress_AddressLine2;
    }

    public function getHomeAddressAddressLine3(): string
    {
        return $this->homeAddress_AddressLine3;
    }

    public function setHomeAddressAddressLine3(string $homeAddress_AddressLine3): void
    {
        $this->homeAddress_AddressLine3 = $homeAddress_AddressLine3;
    }

    public function getHomeAddressAddressLine4(): string
    {
        return $this->homeAddress_AddressLine4;
    }

    public function setHomeAddressAddressLine4(string $homeAddress_AddressLine4): void
    {
        $this->homeAddress_AddressLine4 = $homeAddress_AddressLine4;
    }

    public function getHomeAddressCountryId(): string
    {
        return $this->homeAddress_CountryId;
    }

    public function setHomeAddressCountryId(string $homeAddress_CountryId): void
    {
        $this->homeAddress_CountryId = $homeAddress_CountryId;
    }
}
