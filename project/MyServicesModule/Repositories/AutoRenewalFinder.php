<?php

namespace MyServicesModule\Repositories;

use Doctrine\ORM\QueryBuilder;
use DoctrineModule\SelfClearingIterator;
use Entities\Company;
use Entities\Customer;
use Entities\Event;
use Entities\Payment\Token;
use Entities\Service;
use Entities\ServiceSettings;
use Repositories\CustomerRepository;
use Utils\Date;

class AutoRenewalFinder
{
    /**
     * @var CustomerRepository
     */
    private $customerRepository;

    /**
     * @param CustomerRepository $customerRepository
     */
    public function __construct(CustomerRepository $customerRepository)
    {
        $this->customerRepository = $customerRepository;
    }

    /**
     * @param Date $from
     * @param Date $to
     * @param string $event
     * @return SelfClearingIterator|Customer[]
     */
    public function getCustomersWithServicesDueToAutoRenew(Date $from, Date $to, $event)
    {
        $qb = $this->customerRepository->createQueryBuilder('cu')
            ->distinct(TRUE);
        $qb->innerJoin('cu.companies', 'co')
            ->innerJoin('co.services', 's')
            ->innerJoin('cu.tokens', 't');
        $qb = $this->autoRenewQuery($qb, $from, $to, $event);
        return new SelfClearingIterator($qb);
    }

    /**
     * @param Customer $customer
     * @param Date $from
     * @param Date $to
     * @param string $event
     * @return SelfClearingIterator|Company[]
     */
    public function getCompaniesWithServicesDueToAutoRenew(Customer $customer, Date $from, Date $to, $event)
    {
        $qb = $this->customerRepository->createQueryBuilder();
        $qb->select('co,s')->from(Company::class, 'co')
            ->innerJoin('co.services', 's')
            ->innerJoin(Token::class, 't', 'WITH', 't.customer = :customer');
        $qb = $this->autoRenewQuery($qb, $from, $to, $event);
        $qb->andWhere('co.customer = :customer')
            ->setParameter('customer', $customer->getId());
        return $qb->getQuery()->getResult();
    }

    /**
     * @param QueryBuilder $qb
     * @param Date $from
     * @param Date $to
     * @param $event
     * @return QueryBuilder
     */
    private function autoRenewQuery(QueryBuilder $qb, Date $from, Date $to, $event)
    {
        $qb->innerJoin(ServiceSettings::class, 'ss', 'WITH', 's.company = ss.company AND  ss.serviceTypeId = s.serviceTypeId')
            ->leftJoin(Service::class, 'st', 'WITH', 'st.company = s.company
                    AND st.serviceTypeId = s.serviceTypeId
                    AND st.parent IS NULL
                    AND st.stateId = :enabledService
                    AND st.dtStart IS NOT NULL AND st.dtExpires IS NOT NULL
                    AND st.dtExpires > s.dtExpires')
            ->leftJoin(Event::class, 'e', 'WITH', 's.serviceId = e.objectId AND e.eventKey = :eventKey')
            ->where('s.parent IS NULL')
            ->andWhere('s.stateId = :enabledService')
            ->andWhere('s.dtStart IS NOT NULL AND s.dtExpires IS NOT NULL')
            ->andWhere('s.dtExpires BETWEEN :expiresFrom AND :expiresTo')
            ->andWhere('t.cardExpiryDate >= s.dtExpires')
            ->andWhere('t.sageStatus = :activeSageStatus')
            ->andWhere('ss.isAutoRenewalEnabled = :autoRenewalEnabled')
            ->andWhere('e.eventId IS NULL')
            ->andWhere('st.serviceId IS NULL');
        $qb->setParameter('enabledService', Service::STATE_ENABLED)
            ->setParameter('eventKey', $event)
            ->setParameter('expiresFrom', $from)
            ->setParameter('expiresTo', $to)
            ->setParameter('activeSageStatus', Token::SAGE_STATUS_ACTIVE)
            ->setParameter('autoRenewalEnabled', TRUE);
        return $qb;
    }

}