<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessBankOffers;

use Entities\Company;

class Factory
{
    public function makeRequest(Company $company, array $payload): Request
    {
        return new Request(
            $company,
            $payload['offers'],
        );
    }

    public function makeResponse(): Response
    {
        return new Response();
    }
}
