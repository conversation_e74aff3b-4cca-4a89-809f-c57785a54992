<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\UseCase\IncorporationProcessLog\Api\CreateLog;

use CompanyIncorporationModule\Dto\Enum\Step;
use CompanyIncorporationModule\Dto\Enum\SubStep;
use Entities\Company;

class Request
{
    public function __construct(
        public Company $company,
        public bool $newIncorporationProcess,
        public Step $step,
        public SubStep $subStep,
        public array $data,
        public array $routeParams,
    ) {
    }
}
