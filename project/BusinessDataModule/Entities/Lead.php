<?php

namespace BusinessDataModule\Entities;

use BusinessDataModule\Responses\Adverts\Advert;
use BusinessServicesModule\Entities\Lead as BusinessLead;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;
use Utils\Date;

/**
 * @ORM\Entity(repositoryClass="\BusinessDataModule\Repositories\BdgRepository")
 * @ORM\Table(name="bdg_leads")
 */
class Lead
{
    public const BDG_PARTNER_NAME_BARCLAYS = 'Barclays';

    /**
     * @var UuidInterface
     * @Orm\Column(name="bdgLeadId", type="uuid")
     * @ORM\Id
     */
    private $id;

    /**
     * @var BusinessLead
     *
     * @ORM\OneToOne(targetEntity="BusinessServicesModule\Entities\Lead", inversedBy="bdgLead", fetch="EAGER")
     * @ORM\JoinColumn(name="leadId", referencedColumnName="id", nullable=false)
     */
    private $lead;

    /**
     * @var string
     * @ORM\Column()
     */
    private $advertId;

    /**
     * @var string
     * @ORM\Column()
     */
    private $advertiserName;

    /**
     * @var Rule[]|Collection
     * @Orm\OneToMany(targetEntity = "Rule", mappedBy = "bdgLead")
     */
    private $rules;

    /**
     * @ORM\Embedded(class = "BusinessDataModule\Entities\FollowUp", columnPrefix = "followUp")
     * @var FollowUp
     */
    private $followup;

    /**
     * @var bool
     * @ORM\Column(type="boolean")
     */
    private $termsAgreed;

    /**
     * @var bool
     * @ORM\Column(type="boolean")
     */
    private $fscsAgreed;

    /**
     * @var string|null
     * @ORM\Column()
     */
    private $referral;

    /**
     * @var ContactRule[]|Collection
     * @Orm\OneToMany(targetEntity = "ContactRule", mappedBy = "bdgLead")
     */
    private $contactRules;

    /**
     * @ORM\Column()
     * @var string|null
     */
    private $preferredContactTime;

    /**
     * @ORM\Column()
     * @var string|null
     */
    private $nameOnCard;

    /**
     * @ORM\Column()
     * @var string|null
     */
    private $externalId;

    /**
     * @var DateTime|null
     * @ORM\Column(type="datetime")
     */
    private $dateSent;

    /**
     * @var string|null
     * @ORM\Column()
     */
    private $request;

    /**
     * @var int|null
     * @ORM\Column(type="integer")
     */
    private $responseCode;

    /**
     * @var string|null
     * @ORM\Column()
     */
    private $response;

    /**
     * @var float|null
     * @ORM\Column(type="float")
     */
    private $amount;

    /**
     * 202: Received
     * 102: In Progress
     * 201: Closed
     * 204: Invalid
     *
     * @var string|null
     * @ORM\Column()
     */
    private $status;

    /**
     * @var DateTime
     *
     * @ORM\Column(type="datetime")
     * @Gedmo\Timestampable(on="create")
     */
    private $dtc;

    /**
     * @var DateTime
     *
     * @ORM\Column(type="datetime")
     * @Gedmo\Timestampable(on="create", on="update")
     */
    private $dtm;

    public function __construct(
        BusinessLead $lead,
        string $advertId,
        string $advertiserName,
        FollowUp $followup,
        bool $termsAgreed,
        bool $fscsAgreed
    ) {
        /** @noinspection PhpUnhandledExceptionInspection */
        $this->id = Uuid::uuid4();
        $this->lead = $lead;
        $this->advertId = $advertId;
        $this->advertiserName = $advertiserName;
        $this->followup = $followup;
        $this->termsAgreed = $termsAgreed;
        $this->fscsAgreed = $fscsAgreed;
        $this->contactRules = new ArrayCollection();
        $this->rules = new ArrayCollection();
    }

    public static function fromAdvert(BusinessLead $lead, Advert $advert): self
    {
        $attributes = $advert->getAttributes();

        $self = new self(
            $lead,
            $advert->getId(),
            $attributes->getAdvertiserName(),
            FollowUp::fromResponse($attributes->getFollowUp()),
            $attributes->termsShouldBeSigned(),
            $attributes->fscsConfirmRequired()
        );
        $lead->setBdgLead($self);

        return $self;
    }

    public function getId(): UuidInterface
    {
        return $this->id;
    }

    public function getBusinessLead(): BusinessLead
    {
        return $this->lead;
    }

    public function setBusinessLead(BusinessLead $lead)
    {
        $this->lead = $lead;
    }

    public function getAdvertId(): string
    {
        return $this->advertId;
    }

    public function setAdvertId(string $advertId)
    {
        $this->advertId = $advertId;
    }

    public function getAdvertiserName(): string
    {
        return $this->advertiserName;
    }

    public function setAdvertiserName(string $advertiserName)
    {
        $this->advertiserName = $advertiserName;
    }

    /**
     * @return Rule[]|Collection
     */
    public function getRules(): Collection
    {
        return $this->rules;
    }

    public function getFollowup(): FollowUp
    {
        return $this->followup;
    }

    public function setFollowup(FollowUp $followup)
    {
        $this->followup = $followup;
    }

    public function areTermsAgreed(): bool
    {
        return $this->termsAgreed;
    }

    public function setTermsAgreed(bool $termsAgreed)
    {
        $this->termsAgreed = $termsAgreed;
    }

    public function isFscsAgreed(): bool
    {
        return $this->fscsAgreed;
    }

    public function setFscsAgreed(bool $fscsAgreed)
    {
        $this->fscsAgreed = $fscsAgreed;
    }

    /**
     * @return ContactRule[]|Collection
     */
    public function getContactRules(): Collection
    {
        return $this->contactRules;
    }

    public function getPreferredContactTime(): ?string
    {
        return $this->preferredContactTime;
    }

    public function setPreferredContactTime(?string $time)
    {
        $this->preferredContactTime = $time;
    }

    public function getNameOnCard(): ?string
    {
        return $this->nameOnCard;
    }

    public function setNameOnCard(?string $name)
    {
        $this->nameOnCard = $name;
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(?string $externalId)
    {
        $this->externalId = $externalId;
    }

    public function getDateSent(): ?DateTime
    {
        return $this->dateSent;
    }

    public function setDateSent(?DateTime $dateSent)
    {
        $this->dateSent = $dateSent;
    }

    public function getRequest(): ?string
    {
        return $this->request;
    }

    public function setRequest(?string $request)
    {
        $this->request = $request;
    }

    public function getResponseCode(): int
    {
        return $this->responseCode;
    }

    public function setResponseCode(?int $responseCode)
    {
        $this->responseCode = $responseCode;
    }

    public function getResponse(): ?string
    {
        return $this->response;
    }

    public function setResponse(?string $response)
    {
        $this->response = $response;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function setAmount(?float $amount)
    {
        $this->amount = $amount;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status)
    {
        $this->status = $status;
    }

    public function getDtc(): DateTime
    {
        return $this->dtc;
    }

    public function setDtc(DateTime $dtc)
    {
        $this->dtc = $dtc;
    }

    public function getDtm(): DateTime
    {
        return $this->dtm;
    }

    public function addRule(Rule $rule)
    {
        $this->rules->add($rule);
    }

    public function addContactRule(ContactRule $contact)
    {
        $this->contactRules->add($contact);
    }

    public function getReferral(): ?string
    {
        return $this->referral;
    }

    public function setReferral(?string $referral)
    {
        $this->referral = $referral;
    }

    public function getDaysOld(): int
    {
        $today = new Date();
        $diff = date_diff($today, Date::createFromDateTime($this->getDtc()), true);
        return intval($diff->format("%d"));
    }
}
