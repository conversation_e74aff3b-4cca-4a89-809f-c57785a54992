<div class="summary-item d-flex justify-content-between p-0">
  <p class="item-title" style="max-width: 70%;">
      {$item->getLngTitle()|strip_tags}
      {if $item->companyName}
          for {$item->companyName}
      {/if}
  </p>
  <!--div class="col-xs-1 pad0 text-center">
          {if !empty($item->basketText)}
          <p class="margin0 reset-child-paragraph">&nbsp;
          <i class="fa fa-question-circle black60" data-toggle="tooltip" data-placement="bottom" title="{$item->basketText|markDown}"></i>&nbsp;
          </p>
          {/if}
  </div-->
    <div class="d-flex justify-content-end">
        <p style="font-weight: 600;">
            {if $item->isFree()}
                FREE
            {else}
                {$item->price|currency}
            {/if}
        </p>
      {if $canBeRemoved}
          <form  class="ms-3 text-right" action="{url route='basket_module_basket_upgrade_remove_product'}" method="POST">
              <input type="hidden" name="itemId" value="{$itemId}" />
              <button title="Remove {$item->getLngTitle()}" class="btn-link pad0 remove-btn"
                      {if $item->hasRemoveFromBasketConfirmation()}data-confirm="{$item->getRemoveFromBasketConfirmation()}"{/if}>
                  <!--i class="fa fa-remove removeitem product-basket-remove"
                       data-product-id="{$item->id}"
                       data-product-name="{$item->getLngTitle()}"
                       data-product-quantity="1"
                       data-product-price="{$item->price}"
                    ></i-->
                  <i class="fa-solid fa-circle-xmark mr-3" style="padding-top: 10%; color: #ccc; margin-right: 0.5rem;"
                     data-product-id="{$item->id}"
                     data-product-name="{$item->getLngTitle()}"
                     data-product-quantity="1"
                     data-product-price="{$item->price}"
                  ></i>
              </button>
          </form>
      {/if}
  </div>
</div>
