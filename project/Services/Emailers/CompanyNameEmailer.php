<?php

namespace Services\Emailers;

use EmailModule\IEmailGateway;
use EmailModule\Loaders\IEmailLoader;
use Models\OldModels\Customer;
use Utils\File;
use Utils\Helpers\ArrayHelper;

class CompanyNameEmailer extends EmailerAbstract
{

    const CNCH_REJECTED_EMAIL = 849;
    const CNCH_ACCEPTED_EMAIL = 848;

    const EMAIL_NAME = [
        self::CNCH_ACCEPTED_EMAIL => 'Name Change Accepted',
        self::CNCH_REJECTED_EMAIL => 'Name Change Rejected'
    ];

    /**
     * @var IEmailLoader
     */
    private $emailLoader;

    public function __construct(
        IEmailGateway $emailService,
        IEmailLoader $emailLoader
    )
    {
        $this->emailLoader = $emailLoader;
        parent::__construct($emailService);
    }

    /**
     * @param Customer $customer
     * @param array $replace
     * @param array $return
     */
    public function nameChangeAcceptEmail(Customer $customer, array $replace, array $return)
    {
        $email = $this->emailLoader->getHtmlEmailById(
            self::CNCH_ACCEPTED_EMAIL,
            File::fromExistingPath(
                sprintf('%s%s', EMAIL_DIR, '/cms/Incorporation/Company-Change/name-change-accepted.html')
            ),
            [
                'companyName' => $replace['[COMPANY_NAME]'],
                'firstName' => $replace['[FIRSTNAME]']
            ]
        );
        $email->addTo($customer->email);
        $email->setName(ArrayHelper::get(self::EMAIL_NAME, self::CNCH_ACCEPTED_EMAIL));
        $email->addAttachment($return['attachmentPath']);
        $this->emailService->send($email, $customer);
    }

    /**
     * @param Customer $customer
     * @param array $replace
     */
    public function nameChangerejectEmail(Customer $customer, array $replace)
    {
        $email = $this->emailLoader->getHtmlEmailById(
            self::CNCH_REJECTED_EMAIL,
            File::fromExistingPath(
                sprintf('%s%s', EMAIL_DIR, '/cms/Incorporation/Company-Change/name-change-rejected.html')
            ),
            [
                'companyName' => $replace['[COMPANY_NAME]'],
                'firstName' => $replace['[FIRSTNAME]'],
                'rejectText' => $replace['[REJECT_TEXT]']
            ]
        );
        $email->addTo($customer->email);
        $email->setName(ArrayHelper::get(self::EMAIL_NAME, self::CNCH_REJECTED_EMAIL));
        $this->emailService->send($email, $customer);
    }

}

