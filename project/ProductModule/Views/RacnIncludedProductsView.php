<?php

namespace ProductModule\Views;

class RacnIncludedProductsView
{
    /**
     * @var int
     */
    private $companyId;

    /**
     * @var string
     */
    private $companyName;

    /**
     * @var int
     */
    private $companyNumber;

    /**
     * @var int
     */
    private $customerId;

    /**
     * @var string|null
     */
    private $accountsLastMadeUpDate;

    /**
     * @var string|null
     */
    private $returnsLastMadeUpDate;

    /**
     * @var string
     */
    private $accountsNextDueDate;

    /**
     * @var string
     */
    private $returnsNextDueDate;

    /**
     * @var string
     */
    private $returnsNextMadeDueDate;

    /**
     * @var string
     */
    private $serviceStatus;

    /**
     * @var string|null
     */
    private $accountsNextMadeDueDate;

    /**
     * @var string|null
     */
    private $accountsOverdue;

    /**
     * @var string|null
     */
    private $returnsOverdue;

    public function __construct(
        int $companyId,
        string $companyName,
        int $companyNumber,
        int $customerId,
        string $accountsNextDueDate,
        string $returnsNextDueDate,
        string $serviceStatus,
        string $returnsNextMadeDueDate,
        ?string $accountsLastMadeUpDate = null,
        ?string $returnsLastMadeUpDate = null,
        ?string $accountsNextMadeDueDate = null,
        ?string $accountsOverdue = null,
        ?string $returnsOverdue = null
    )
    {
        $this->companyId = $companyId;
        $this->companyName = $companyName;
        $this->companyNumber = $companyNumber;
        $this->customerId = $customerId;
        $this->accountsLastMadeUpDate = $accountsLastMadeUpDate;
        $this->returnsLastMadeUpDate = $returnsLastMadeUpDate;
        $this->accountsNextDueDate = $accountsNextDueDate;
        $this->returnsNextDueDate = $returnsNextDueDate;
        $this->accountsNextMadeDueDate = $accountsNextMadeDueDate;
        $this->returnsNextMadeDueDate = $returnsNextMadeDueDate;
        $this->accountsOverdue = $accountsOverdue;
        $this->returnsOverdue = $returnsOverdue;
        $this->serviceStatus = $serviceStatus;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function getCompanyNumber(): int
    {
        return $this->companyNumber;
    }

    public function getCustomerId(): int
    {
        return $this->customerId;
    }

    public function getAccountsLastMadeUpDate(): ?string
    {
        return $this->accountsLastMadeUpDate;
    }

    public function getReturnsLastMadeUpDate(): ?string
    {
        return $this->returnsLastMadeUpDate;
    }

    public function getAccountsNextDueDate(): string
    {
        return $this->accountsNextDueDate;
    }

    public function getReturnsNextDueDate(): string
    {
        return $this->returnsNextDueDate;
    }

    public function getReturnsNextMadeDueDate(): string
    {
        return $this->returnsNextMadeDueDate;
    }

    public function getserviceStatus(): string
    {
        return $this->serviceStatus;
    }

    public function getAccountsNextMadeDueDate(): ?string
    {
        return $this->accountsNextMadeDueDate;
    }

    public function getAccountsOverdue(): ?string
    {
        return $this->accountsOverdue;
    }

    public function getReturnsOverdue(): ?string
    {
        return $this->returnsOverdue;
    }
}