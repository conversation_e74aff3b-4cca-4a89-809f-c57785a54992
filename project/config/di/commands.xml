<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service id="console.commands.sage.tokens.sync_command" class="SagePayToken\Reporting\Commands\Tokens\SyncCommand">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="loggers.nikolai_logger_psr"/>
            <argument type="service" id="services.payment.token_service"/>
            <argument type="service" id="sagepay.reporting.sagepay"/>
        </service>

        <service id="console.commands.packages.activate_command" class="Console\Commands\Packages\ActivateCommand">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="loggers.nikolai_logger"/>
        </service>

        <service id="console.commands.order_items.fill_company_references_command" class="Console\Commands\OrderItems\FillCompanyReferencesCommand">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="loggers.nikolai_logger"/>
        </service>

        <service id="console.commands.services.shorten_by_one_day_command" class="Console\Commands\Services\ShortenByOneDayCommand">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="loggers.nikolai_logger"/>
        </service>

        <service id="console.commands.services.set_upgrade_downgrade_command" class="Console\Commands\Services\SetUpgradeDowngradeCommand">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="loggers.nikolai_logger"/>
            <argument type="service" id="services.service_service"/>
        </service>

        <service id="console.commands.services.update_missing_children_command" class="Console\Commands\Services\UpdateMissingChildrenCommand">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="loggers.nikolai_logger"/>
            <argument type="service" id="services.service_service"/>
        </service>

        <service id="console.commands.services.assign_order_items_command" class="Console\Commands\Services\AssignOrderItemsCommand">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="monolog.logger.assign_order_items"/>
            <argument type="service" id="dibi"/>
            <argument type="service" id="services.service_service"/>
            <argument type="service" id="services.order_service"/>
            <argument type="service" id="services.order_item_service"/>
        </service>

        <service id="console.commands.services.remove_orphans_command" class="Console\Commands\Services\RemoveOrphansCommand">
            <argument type="service" id="loggers.nikolai_logger"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
        </service>

        <service id="console.commands.services.remove_refunded_command" class="Console\Commands\Services\RemoveRefundedCommand">
            <argument type="service" id="loggers.nikolai_logger"/>
            <argument type="service" id="services.service_service"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
        </service>

        <service id="commands.nikolai.order_items.mark_items_as_exported_command" class="Commands\Nikolai\OrderItems\MarkItemsAsExportedCommand">
            <argument type="service" id="services.order_item_service"/>
            <argument type="service" id="monolog_logger.mark_order_items_as_exported_command"/>
        </service>

        <service id="console.commands.vo_service.update_product_command" class="Console\Commands\VoService\UpdateProductNameCommand">
            <argument type="service" id="monolog_logger.update_vo_queue_product_name"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="vo_service_module.services.vo_service_queue_service"/>
            <argument type="service" id="services.node_service"/>
        </service>

        <service id="console.commands.cashbacks.outdate_old_cashbacks_command" class="Console\Commands\Cashbacks\OutdateOldCashbacksCommand">
            <argument type="service" id="monolog_logger.outdate_old_cashbacks"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
        </service>

        <service class="Console\Commands\Services\RenewToddingtonServicesCommand" id="console.commands.services.renew_toddington_services_command">
            <argument type="service" id="monolog.logger.renew_toddington_services"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="services.customer_service"/>
            <argument type="service" id="services.product_service"/>
        </service>

        <service id="console.commands.services.create_international_services_command" class="Console\Commands\Services\CreateInternationalServicesCommand">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="monolog.logger.create_international_services"/>
            <argument type="service" id="services.service_service"/>
            <argument type="service" id="services.company_service"/>
            <argument type="service" id="services.node_service"/>
        </service>

        <service class="Console\Commands\Submissions\ResendFakedCommand" id="console.commands.submissions.resend_faked_command">
            <argument type="service" id="monolog.logger.resend_faked_submissions"/>
            <argument type="service" id="dibi"/>
            <argument type="service" id="services.submission_service"/>
        </service>

        <service class="Console\Commands\OrderItems\UpdateRefundedOrderItemsCommand" id="console.commands.order_items.update_refunded_order_items_command">
            <argument type="service" id="monolog.logger.update_refunded_items_from_lpl"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="repositories.order_item_repository"/>
        </service>
    </services>
</container>
