<?xml version="1.0" ?>

<!-- TODO: Remove adapter implementation from utils, implement in actual repositories-->

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service id="utils.text.parser.filters.ui_elements.button_element" class="Utils\Text\Parser\Filters\UiElements\Element">
            <argument type="service" id="ui_helper.view_helper"/>
        </service>

        <service id="utils.text.parser.filters.ui_elements.buy_button_element_factory" class="Factories\Utils\Text\Parser\Filters\UiElements\BuyButtonElementFactory">
            <argument type="service" id="ui_helper.view_helper"/>
            <argument type="service" id="common_module.url_generator"/>
            <argument>basket_module_product_basket_add_product</argument>
            <argument>productId</argument>
        </service>

        <service id="utils.text.parser.filters.ui_elements.buy_package_button_element_factory" class="Factories\Utils\Text\Parser\Filters\UiElements\BuyButtonElementFactory">
            <argument type="service" id="ui_helper.view_helper"/>
            <argument type="service" id="common_module.url_generator"/>
            <argument>basket_module_basket_upgrade_add_package</argument>
            <argument>packageId</argument>
        </service>

        <service id="utils.text.parser.filters.ui_elements.buy_button_element" class="Utils\Text\Parser\Filters\UiElements\IElement">
            <factory service="utils.text.parser.filters.ui_elements.buy_button_element_factory" method="createElement"/>
        </service>

        <service id="utils.text.parser.filters.ui_elements.buy_package_button_element" class="Utils\Text\Parser\Filters\UiElements\IElement">
            <factory service="utils.text.parser.filters.ui_elements.buy_package_button_element_factory" method="createElement"/>
        </service>

        <service id="utils.text.parser.filters.ui_filter" class="Utils\Text\Parser\Filters\UiFilter">
            <call method="addElement">
                <argument>button</argument>
                <argument type="service" id="utils.text.parser.filters.ui_elements.button_element"/>
            </call>
            <call method="addElement">
                <argument>button_new</argument>
                <argument type="service" id="utils.text.parser.filters.ui_elements.button_element"/>
            </call>
            <call method="addElement">
                <argument>buy_button</argument>
                <argument type="service" id="utils.text.parser.filters.ui_elements.buy_button_element"/>
            </call>
            <call method="addElement">
                <argument>buy_button_new</argument>
                <argument type="service" id="utils.text.parser.filters.ui_elements.buy_button_element"/>
            </call>
            <call method="addElement">
                <argument>buy_package_button</argument>
                <argument type="service" id="utils.text.parser.filters.ui_elements.buy_package_button_element"/>
            </call>
        </service>

        <service id="utils.text.parser.filters.ghost_filter" class="Utils\Text\Parser\Filters\GhostFilter">
            <argument type="service" id="ghost_module.services.ghost_service"/>
        </service>

        <service id="utils.text.parser.filters.price_filter" class="Utils\Text\Parser\Filters\PriceFilter">
            <argument type="service" id="repositories.nodes.product_repository"/>
        </service>

        <service id="utils.text.parser.filters.price_diff_filter" class="Utils\Text\Parser\Filters\PriceDiffFilter">
            <argument type="service" id="repositories.nodes.product_repository"/>
        </service>

        <service id="utils.text.parser.filters.cash_back_amount_filter" class="Utils\Text\Parser\Filters\CashBackAmountFilter">
            <argument type="service" id="business_services_module.providers.cashback_provider"/>
        </service>

        <service id="utils.text.parser.filters.page_link_filter" class="Utils\Text\Parser\Filters\PageLinkFilter">
            <argument type="service" id="common_module.url_generator"/>
        </service>

        <service id="utils.text.parser.filters.basket_link_filter" class="Utils\Text\Parser\Filters\BasketLinkFilter">
            <argument type="service" id="common_module.url_generator"/>
            <argument>basket_module_product_basket_add_product</argument>
            <argument>productId</argument>
        </service>

        <service id="utils.text.parser.parser" class="Utils\Text\Parser\Parser">
            <call method="addFilter">
                <argument type="service" id="utils.text.parser.filters.ghost_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="utils.text.parser.filters.price_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="utils.text.parser.filters.page_link_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="utils.text.parser.filters.basket_link_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="utils.text.parser.filters.price_diff_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="utils.text.parser.filters.ui_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="utils.text.parser.filters.cash_back_amount_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="package_module.filters.special_price_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="package_module.filters.special_price_bool_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="package_module.filters.special_price_notification_filter"/>
            </call>
            <call method="addFilter">
                <argument type="service" id="package_module.filters.cashback_banks_list_filter"/>
            </call>
        </service>
    </services>
</container>
