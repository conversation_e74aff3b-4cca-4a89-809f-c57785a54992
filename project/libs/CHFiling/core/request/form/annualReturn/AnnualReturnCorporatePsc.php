<?php

namespace Libs\CHFiling\Core\Request\Form\AnnualReturn;

use DateTime;
use Exception;
use Libs\CHFiling\Core\CHFiling;
use Libs\CHFiling\Core\UtilityClass\CorporatePsc;

final class AnnualReturnCorporatePsc extends CorporatePsc
{
    /**
     * @var int
     */
    private $annualReturnOfficerId;

    /**
     * @var int
     */
    private $formSubmissionId;

    /**
     * @var string
     */
    private static $type = 'PSC';

    /**
     * @var bool
     */
    private static $corporate = 1;

    /**
     * @var string
     */
    private static $tableName = 'ch_annual_return_officer';

    /**
     * @var bool
     */
    private $alreadyExistingPsc = FALSE;

    /**
     * @var DateTime
     */
    private $cessationDate;

    /**
     * @var DateTime
     */
    private $dateOfChange;

    /**
     * @var array|null
     */
    private $originalData;

    /**
     * @param int $formSubmissionId
     * @param int $annualReturnOfficerId
     * @throws Exception
     */
    public function __construct($formSubmissionId, $annualReturnOfficerId = NULL)
    {
        if (!preg_match('/^\d+$/', $formSubmissionId)) {
            throw new Exception('form submission id has to be integer');
        }

        parent::__construct();

        if (!$annualReturnOfficerId) {
            $this->annualReturnOfficerId = NULL;
            $this->formSubmissionId = $formSubmissionId;

            return;
        }

        if (!preg_match('/^\d+$/', $annualReturnOfficerId)) {
            throw new Exception('incorporation member id has to be integer');
        }

        $sql = "SELECT * FROM `" . self::$tableName . "`
			    WHERE `annual_return_officer_id` = $annualReturnOfficerId AND `form_submission_id` = $formSubmissionId";

        if (!$result = CHFiling::getDb()->fetchRow($sql)) {
            throw new Exception("Record doesn't exist");
        }

        $this->annualReturnOfficerId = $result['annual_return_officer_id'];
        $this->formSubmissionId = $result['form_submission_id'];
        $this->setFields($result);
    }

    /**
     * @param array $data
     */
    public function setFields(array $data)
    {
        parent::setFields($data);
        $this->alreadyExistingPsc = (bool) $data['existing_officer'];
        $this->cessationDate = isset($data['cessation_date']) ? new DateTime($data['cessation_date']) : NULL;
        $this->originalData = isset($data['before_changes']) ? json_decode($data['before_changes'], TRUE) : NULL;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return self::$type;
    }

    public function remove()
    {
        CHFiling::getDb()->delete(self::$tableName, 'annual_return_officer_id = ' . $this->annualReturnOfficerId);
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->annualReturnOfficerId;
    }

    /**
     * @return bool
     */
    public function isComplete()
    {
        $allowedFields = array(
            'corporate_name',
            'premise', 'street', 'post_town', 'country',
            'identification_type'
        );

        $fields = array_merge(
            $this->getCorporate()->getFields(),
            $this->getAddress()->getFields(),
            $this->getIdentification()->getFields()
        );
        $fields['identification_type'] = $this->getIdentification()->getTypeName();

        foreach ($allowedFields as $v) {
            if (!isset($fields[$v]) || $fields[$v] === NULL) {
                return 0;
            }
        }

        return 1;
    }

    /**
     * @throws Exception
     */
    public function save()
    {
        if (!$this->isComplete()) {
            throw new Exception('You need to set all compulsory fielsd');
        }

        $data = $this->getData();

        if (isset($this->annualReturnOfficerId) && $this->annualReturnOfficerId !== NULL) {
            CHFiling::getDb()->update(self::$tableName, $data, 'annual_return_officer_id = ' . $this->annualReturnOfficerId);
        } else {
            $data['before_changes'] = json_encode($this->getTrackedData());
            CHFiling::getDb()->insert(self::$tableName, $data);
            $this->annualReturnOfficerId = CHFiling::getDb()->lastInsertId();
        }
    }

    /**
     * @return array
     * @throws Exception
     */
    public function getData()
    {
        $address = $this->getAddress()->getFields();
        unset($address['secure_address_ind']);
        $data = array_merge(
            $this->getCorporate()->getFields(),
            $address,
            $this->getIdentification()->getFields(),
            $this->getNatureOfControl()->getFields()
        );

        $data['identification_type'] = $this->getIdentification()->getTypeName();
        $data['form_submission_id'] = $this->formSubmissionId;
        $data['corporate'] = self::$corporate;
        $data['type'] = self::$type;
        $data['consentToAct'] = $this->hasConsentToAct();
        $data['existing_officer'] = $this->isAlreadyExistingPsc();
        $data['notification_date'] = $this->getNotificationDate() ? $this->getNotificationDate()->format('Y-m-d') : NULL;
        $data['cessation_date'] = $this->getCessationDate() ? $this->getCessationDate()->format('Y-m-d') : NULL;
        $data['date_of_change'] = $this->getDateOfChange() ? $this->getDateOfChange()->format('Y-m-d') : NULL;

        return $data;
    }

    /**
     * @return array
     */
    public function getTrackedData()
    {
        $data = $this->getData();

        unset($data['form_submission_id']);
        unset($data['consentToAct']);
        unset($data['existing_officer']);
        unset($data['notification_date']);
        unset($data['cessation_date']);
        unset($data['date_of_change']);

        return $data;
    }

    /**
     * @return bool
     */
    public function isChanged()
    {
        return !empty(array_diff($this->getTrackedData(), $this->originalData));
    }

    /**
     * @return AnnualReturnCorporatePsc
     */
    public function withOriginalData()
    {
        $psc = new self($this->formSubmissionId);
        $psc->setFields(array_merge($this->getData(), array_intersect_key($this->originalData, $this->getTrackedData())));

        return $psc;
    }

    /**
     * @return boolean
     */
    public function isAlreadyExistingPsc()
    {
        return $this->alreadyExistingPsc;
    }

    /**
     * @param boolean $alreadyExistingPsc
     */
    public function setAlreadyExistingPsc($alreadyExistingPsc)
    {
        $this->alreadyExistingPsc = $alreadyExistingPsc;
    }

    /**
     * @return DateTime
     */
    public function getCessationDate()
    {
        return $this->cessationDate;
    }

    /**
     * @param DateTime $cessationDate
     */
    public function setCessationDate(DateTime $cessationDate)
    {
        $this->cessationDate = $cessationDate;
    }

    public function unsetCessationDate()
    {
        $this->cessationDate = NULL;
    }
}

