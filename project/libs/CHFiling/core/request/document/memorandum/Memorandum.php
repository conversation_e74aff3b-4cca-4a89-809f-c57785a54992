<?php

namespace Libs\CHFiling\Core\Request\Document\Memorandum;

use CompanyFormationModule\Entities\IShareholder;
use CompanyFormationModule\Entities\ShareholderCorporate;
use CompanyFormationModule\Entities\ShareholderPerson;
use CompanyFormationModule\Files\ArticlesGenerator;
use CompanyFormationModule\Services\ShareholderService;
use FPDF;
use Libs\CHFiling\Core\Company;
use Services\CompanyService;
use Services\Registry;

class Memorandum {

    /**
     * @var FPDF
     */
    private $fpdf;

    /**
     * @var string
     */
    private $companyName;

    /**
     * @var IShareholder[] | NULL
     */
    private $subscribers;

    /**
     * @var IShareholder[] | NULL
     */
    private $corpSubscribers;

    /**
     * @var bool
     */
    private $shareCapital;

    /**
     * @var ShareholderService
     */
    private $shareholderService;

    /**
     * @var Company
     */
    private $company;

    /**
     * @var CompanyService
     */
    private $companyService;

    public function __construct($companyName, array $subscribers, array $corpSubscribers, $shareCapital = TRUE, Company $company = NULL) {
        $this->companyName = $companyName;
        $this->subscribers = $subscribers;
        $this->corpSubscribers = $corpSubscribers;
        $this->shareCapital = (bool) $shareCapital;

        $this->fpdf = new FPDF();
        $this->fpdf->AliasNbPages();

        $this->shareholderService = Registry::getService('company_formation_module.services.shareholder_service');
        $this->companyService = Registry::getService('services.company_service');
        $this->company = $company;
    }

    private function createPdf() {
        if ($this->shareholderService->hasMultipleShareClasses($this->company)) {
            $articlesGenerator = new ArticlesGenerator($this->fpdf);
            $this->fpdf = $articlesGenerator->generateArticlesForMultipleShareClasses(
                $this->companyService->getCompanyById($this->company->getId())
            );
        }

        $this->fpdf->AddPage();
        $this->fpdf->SetFont('Helvetica', '', 16);
        $this->fpdf->MultiCell(190, 5, 'Companies Act 2006', 0, 'C');

        $this->fpdf->Ln();

        $this->fpdf->SetFont('Helvetica', '', 12);

        $shareCapital   = ($this->shareCapital) ? '' : ' NOT' ;
        $schedule       = ($this->shareCapital) ? '1' : '2';
        $share          = ($this->shareCapital) ? ' and to take at least one share each.' : '.';
        $heading = "SCHEDULE $schedule \n COMPANY$shareCapital HAVING A SHARE CAPITAL \n Memorandum of Association of \n " . iconv("UTF-8", "ISO-8859-1//TRANSLIT", $this->companyName);

        $subHeading = 'Each subscriber to this memorandum of association wishes '.
            'to form a company under the Companies Act 2006 and agrees to become '.
            "a member of the company".$share;

        /*
            Name and address of each subscriber
            Authentication by each subscriber
            Authenticated Electronically
            Dated [insert date here]';
         */
        $this->fpdf->MultiCell(190, 5, $heading, 0, 'C');

        $this->fpdf->SetFont('Helvetica', '', 10);

        $this->fpdf->Ln();
        $this->fpdf->Ln();

        $this->fpdf->MultiCell(190, 5, $subHeading, 0, 'J');

        $this->fpdf->Ln();
        $this->fpdf->Ln();

        /** @var ShareholderPerson | ShareholderCorporate $subscriber */
        foreach ($this->subscribers as $subscriber) {
            $forename   = iconv("UTF-8", "ISO-8859-1//TRANSLIT",$subscriber->getPerson()->getForename());
            $surname    = iconv("UTF-8", "ISO-8859-1//TRANSLIT",$subscriber->getPerson()->getSurname());
            $middleName = iconv("UTF-8", "ISO-8859-1//TRANSLIT",$subscriber->getPerson()->getMiddleName()) . ' ';

            if (!isset($middleName) || is_null($middleName)) {
                $middleName = ' ';
            }
            $this->fpdf->MultiCell(190, 5, 'Subscriber:', 'B');
            $this->fpdf->MultiCell(190, 5, $forename . ' ' . $middleName . ' ' . $surname, 0, 'J');

            /* --- fucking companies house changed their mind
            $this->fpdf->Ln();
            $address = $subscriber->getAddress()->getFields();
            foreach($address as $value) {
                if ($value != null || !empty($value)) {
                    $this->fpdf->MultiCell(190, 5, $value, 0, 'J');
                }
            }
            */
            $this->fpdf->Ln();

            $this->fpdf->MultiCell(190, 5, 'Authentication: Authenticated Electronically');

            $this->fpdf->Ln();
        }

        foreach ($this->corpSubscribers as $subscriber) {
            $this->fpdf->MultiCell(190, 5, 'Subscriber:', 'B');
            $this->fpdf->MultiCell(190, 5, $subscriber->getCorporate()->getCorporateName());
            
            /* --- fucking companies house changed their mind again --- */
//            $this->fpdf->MultiCell(190, 5, 'Authorising Person: '.$subscriber->getCorporate()->getForename() .' '.$subscriber->getCorporate()->getSurname(), 0, 'J');

            /* --- fucking companies house changed their mind
            $this->fpdf->Ln();
            $address = $subscriber->getAddress()->getFields();
            foreach($address as $value) {
                if ($value != null || !empty($value)) {
                    $this->fpdf->MultiCell(190, 5, $value, 0, 'J');
                }
            }
            */
            $this->fpdf->Ln();

            $this->fpdf->MultiCell(190, 5, 'Authentication: Authenticated Electronically');

            $this->fpdf->Ln();
        }

        $this->fpdf->Ln();
        $this->fpdf->MultiCell(190, 5, 'Dated: '.date('j M Y'));

    }

    public function getMemorandumPdf() {
        $this->createPdf();

        $this->fpdf->Output('Memorandum.pdf','I');
        exit;
    }

    public function saveMemorandum($filePath) {
        $this->createPdf();

        $this->fpdf->Output($filePath,'F');
    }


}
