<?php

namespace CompanyFormationModule\Facades;

use FrontModule\Forms\CFRegisteredOffice\CFRegisteredOfficeForm;
use CompanyFormationModule\Entities\UpsellQueueItem;
use CompanyFormationModule\Services\UpsellQueueService;
use Entities\Company;
use Services\Payment\TokenService;

class RegisteredOfficeUpsellFacade
{
    /**
     * @var UpsellQueueService
     */
    private $queueService;

    /**
     * @var TokenService
     */
    private $tokenService;

    /**
     * @param UpsellQueueService $queueService
     * @param TokenService $tokenService
     */
    public function __construct(UpsellQueueService $queueService, TokenService $tokenService)
    {
        $this->queueService = $queueService;
        $this->tokenService = $tokenService;
    }

    /**
     * @param Company $company
     */
    public function add(Company $company)
    {
        $setting = $company->getSettings()->getRegisteredOfficeUpsell();

        $type = $setting->getType();

        if (!$type || $type == CFRegisteredOfficeForm::USE_OWN_ADDRESS) {
            return;
        }

        if ($this->queueService->hasExistingUpsell($company, $type)) {
            return;
        }

        $token = $this->tokenService->getTokenById($setting->getTokenId());

        $order = $company->getOrder();
        $item = new UpsellQueueItem(
            $company,
            $order,
            $type,
            $token
        );

        $this->queueService->save($item);
    }
}
