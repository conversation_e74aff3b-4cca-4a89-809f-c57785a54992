<?php

namespace CompanyFormationModule\Services\Director;

use CompanyFormationModule\Entities\IDirector;
use CompanyFormationModule\Exceptions\CompanyNotMatchException;
use Doctrine\ORM\EntityManager;
use Entities\Company;

class DirectorDeleter
{
    /**
     * @var EntityManager
     */
    private $em;

    public function __construct(EntityManager $em)
    {
        $this->em = $em;
    }

    public function deleteDirector(Company $company, IDirector $director): void
    {
        if ($director->getFormSubmission()->getCompany() !== $company) {
            throw new CompanyNotMatchException();
        }

        $this->em->remove($director);
        $this->em->flush($director);
    }
}
