<?php

namespace FeedbackModule\Clients;

use FeedbackModule\Exceptions\FeedbackResponseException;
use FeedbackModule\Responses\FeedbackResponse;
use HttpClient\ClientInterface;
use HttpClient\Requests\Request;
use HttpClient\Requests\RequestInterface;
use HttpClient\Requests\RequestOptions;
use Symfony\Component\Serializer\Exception\UnexpectedValueException;
use Utils\Exceptions\InvalidArgumentException;
use HttpClient\Exceptions\RequestException;
use Utils\Helpers\JsonHelper;

class FeedbackClient
{
    /**
     * @var ClientInterface
     */
    private $client;

    public function __construct(ClientInterface $client)
    {
        $this->client = $client;
    }

    public function getResponse(string $endpoint): FeedbackResponse
    {
        try {
            $request = new Request($endpoint);
            $request->setOptions(new RequestOptions(30, false));
            $request->setQuery(['external_login' => 'true']);

            $response = $this->client->sendRequest($request);
            return FeedbackResponse::fromArray(
                JsonHelper::decode($response->getBody())
            );
        } catch (RequestException $e) {
            throw FeedbackResponseException::requestFailed($e);
        } catch (InvalidArgumentException $e) {
            throw FeedbackResponseException::wrongFormat($e);
        } catch (UnexpectedValueException $e) {
            throw FeedbackResponseException::invalidJson($e);
        }
    }
}
