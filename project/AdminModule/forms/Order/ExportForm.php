<?php

namespace AdminModule\forms\Order;

use FormModule\Types\DateRangeType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;

class ExportForm extends AbstractType
{
    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder->setMethod('POST');
        $builder->add(
            'date_range',
            DateRangeType::class,
            [
                'property_path' => 'datesRange',
                'start_options' => [
                    'required' => FALSE,
                    'label' => 'Start Date',
                ],
                'end_options' => [
                    'required' => FALSE,
                    'label' => 'End Date',
                ]
            ]
        );
        $builder->add(
            'export',
            SubmitType::class,
            [
                'label' => 'Export',
            ]
        );
    }

    /**
     * {@inheritdoc}
     */
    public function getName()
    {
        return 'admin_order_export_form';
    }
}