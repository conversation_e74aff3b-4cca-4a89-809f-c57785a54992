<?php

namespace SentryModule\Subscribers;

use <PERSON><PERSON>\Serializer\EventDispatcher\Events;
use <PERSON><PERSON>\Serializer\EventDispatcher\EventSubscriberInterface;
use <PERSON><PERSON>\Serializer\EventDispatcher\PreDeserializeEvent;
use SentryModule\Dto\ErrorResponse;
use SerializingModule\SerializerSubstractAbstract;

class ErrorDetailResponseSubscriber extends SerializerSubstractAbstract implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            ['event' => Events::PRE_DESERIALIZE, 'method' => 'onPreDeserialize'],
        ];
    }

    public function onPreDeserialize(PreDeserializeEvent $event): void
    {
        if (!$this->supportsClass(ErrorResponse::class, $event->getType()))
            return;

        $data = $event->getData();
        if (isset($data['detail'])) $data = $data['detail'];
        $event->setData($data);
    }

    public function supportsClass(string $class, array $type): bool
    {
        return in_array($class, $type, TRUE);
    }
}