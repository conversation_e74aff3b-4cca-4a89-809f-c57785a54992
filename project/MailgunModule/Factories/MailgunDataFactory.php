<?php

namespace MailgunModule\Factories;

use DateTime;
use EmailModule\IEmail;
use Ramsey\Uuid\Uuid;

class MailgunDataFactory
{
    public static function create(IEmail $email): array
    {
        if ($email->getTemplateName()) {
            return self::template($email);
        }

        return self::html($email);
    }

    private static function html(IEmail $email): array
    {
        return array_merge(
            self::common($email),
            [
                'html' => (string)$email->getBody()
            ]
        );
    }

    private static function template(IEmail $email): array
    {
        if ($email->getTemplateData()) {
            return array_merge(
                self::common($email),
                [
                    'template' => $email->getTemplateName(),
                    'h:X-Mailgun-Variables' => json_encode($email->getTemplateData()),

                ]
            );
        }

        return array_merge(
            self::common($email),
            [
                'template' => $email->getTemplateName()
            ]
        );
    }

    private static function common(IEmail $email): array
    {
        $data = [
            'from' => $email->getFromName() ? "{$email->getFromName()} <{$email->getFromEmail()}>" : $email->getFrom(),
            'to' => implode(',', $email->getToArr()),
            'subject' => $email->getSubject(),
            'v:my-custom-data' => json_encode($email->getAdditionalData()),
            'o:tag' => $email->getTags(),
            'v:msg-id' => $email->getUniqueId()
        ];

        if ($deliveryTime = $email->getDeliveryTime()) {
            $data['o:deliverytime'] = $deliveryTime->format(DateTime::RFC7231);
        }

        if ($email->getCcArr()) {
            $data['cc'] = implode(',', $email->getCcArr());
        }

        if ($email->getBccArr()) {
            $data['bcc'] = implode(',', $email->getBccArr());
        }

        return $data;
    }
}
