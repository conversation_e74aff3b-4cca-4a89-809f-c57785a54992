<?php

namespace PeopleWithSignificantControl\Forms\Old;

use AdminModule\forms\CHForm;
use CompaniesHouse\Repositories\CountriesRepository;
use CompanyModule\Providers\OurServiceAddressProvider;
use Framework\Forms\Controls\DatePickerNew;
use Framework\Forms\Controls\DateSelect;
use DateTime;
use Entities\Company;
use Framework\Forms\Controls\FControl;
use Framework\Forms\FForm;
use Libs\CHFiling\Core\Request\Form\Incorporation\CompanyIncorporation;
use Libs\CHFiling\Core\UtilityClass\Person;
use Libs\Forms\Validators\CHValidator;
use Libs\Forms\Validators\EnglishKeyboardValidator;
use PeopleWithSignificantControl\Providers\PscChoicesProvider;
use Framework\Forms\Controls\Radio;
use Symfony\Component\Routing\RouterInterface;

class PersonPscForm
{
    /**
     * @var Company
     */
    private $company;

    /**
     * @var RouterInterface
     */
    private $router;

    /**
     * @var OurServiceAddressProvider
     */
    private $ourServiceAddressProvider;

    /**
     * @var PersonPscData
     */
    private $data;

    /**
     * @var callable
     */
    private $onValidCallback;

    /**
     * @var PscChoicesProvider
     */
    private $pscChoicesProvider;

    /**
     * @var array
     */
    private $prefillOfficers;

    /**
     * @var array
     */
    private $prefillAddresses;

    /**
     * @var PscFormOptions
     */
    private $formOptions;

    /**
     * @var bool
     */
    private $isAdminView;

    /**
     * @var DateTime
     */
    private $notificationDateValue;

    /**
     * @var DateTime
     */
    private $dateOfChangeValue;

    /**
     * @var CountriesRepository
     */
    private $chCountriesRepository;

    /**
     * @var array
     */
    private $nationalities;

    public function __construct(
        PscChoicesProvider $pscChoicesProvider,
        RouterInterface $router,
        OurServiceAddressProvider $ourServiceAddressProvider,
        Company $company,
        PersonPscData $data,
        callable $onValidCallback,
        PscFormOptions $formOptions,
        CountriesRepository $chCountriesRepository,
        array $nationalities,
        $prefillOfficers = NULL,
        $prefillAddresses = NULL,
        $isAdminView = FALSE
    )
    {
        $this->pscChoicesProvider = $pscChoicesProvider;
        $this->router = $router;
        $this->ourServiceAddressProvider = $ourServiceAddressProvider;
        $this->company = $company;
        $this->data = $data;
        $this->onValidCallback = $onValidCallback;
        $this->formOptions = $formOptions;
        $this->chCountriesRepository = $chCountriesRepository;
        $this->prefillOfficers = $prefillOfficers;
        $this->prefillAddresses = $prefillAddresses;
        $this->isAdminView = $isAdminView;

        $this->notificationDateValue = $this->data->getNotificationDate()
            ? $this->data->getNotificationDate()
            : $this->formOptions->getMadeUpToDate();
        $this->dateOfChangeValue = $this->data->getDateOfChange()
            ? $this->data->getDateOfChange()
            : $this->formOptions->getMadeUpToDate();
        $this->nationalities = $nationalities;
    }

    /**
     * @param string $name
     * @return CHForm
     */
    public function getForm($name)
    {
        $serviceAddress = $this->data->getServiceAddress();
        $residentialAddress = $this->data->getResidentialAddress();
        $fieldValues = [
            'title' => $this->data->getTitle(),
            'forename' => $this->data->getFirstName(),
            'middle_name' => $this->data->getMiddleName(),
            'surname' => $this->data->getLastName(),
            'dob' => $this->data->getDateOfBirth() ? $this->data->getDateOfBirth()->format('Y-m-d') : NULL,
            'nationality' => $this->data->getNationality(),
            'country_of_residence' => $this->data->getCountryOfResidence(),

            'premise' => $serviceAddress->getPremise(),
            'street' => $serviceAddress->getStreet(),
            'thoroughfare' => $serviceAddress->getThoroughfare(),
            'post_town' => $serviceAddress->getPostTown(),
            'county' => $serviceAddress->getCounty(),
            'postcode' => $serviceAddress->getPostcode(),
            'country' => $serviceAddress->getCountry(),

            'residential_premise' => $residentialAddress->getPremise(),
            'residential_street' => $residentialAddress->getStreet(),
            'residential_thoroughfare' => $residentialAddress->getThoroughfare(),
            'residential_post_town' => $residentialAddress->getPostTown(),
            'residential_county' => $residentialAddress->getCounty(),
            'residential_postcode' => $residentialAddress->getPostcode(),
            'residential_country' => $residentialAddress->getCountry(),

            'ownership_of_shares' => $this->data->getOwnershipOfShares(),
            'ownership_of_voting_rights' => $this->data->getOwnershipOfVotingRights(),
            'right_to_appoint_and_remove_directors' => $this->data->getRightToAppointAndRemoveDirectors(),
            'significant_influence_or_control' => $this->data->getSignificantInfluenceOrControl(),

            'notification_date' => $this->notificationDateValue->format('d-m-Y'),
            'date_of_change' => $this->dateOfChangeValue->format('d-m-Y'),
        ];

        $form = $this->buildForm($fieldValues, $name);

        $form->onValid = [$this, 'onValid'];

        return $form;
    }

    /**
     * @param FForm $form
     */
    public function onValid(FForm $form)
    {
        $data = $form->getValues();

        $serviceAddress = new Address;
        $serviceAddress
            ->setPremise($data['premise'])
            ->setStreet($data['street'])
            ->setThoroughfare($data['thoroughfare'])
            ->setPostTown($data['post_town'])
            ->setCounty($data['county'])
            ->setPostcode($data['postcode'])
            ->setCountry($data['country']);

        if ($this->company->canUseOurServiceAddress() && $data['ourServiceAddress']) {
            $msgServiceAdress = $this->ourServiceAddressProvider->getProduct();
            $serviceAddress
                ->setPremise($msgServiceAdress->premise)
                ->setStreet($msgServiceAdress->street)
                ->setThoroughfare($msgServiceAdress->thoroughfare)
                ->setPostTown($msgServiceAdress->post_town)
                ->setCounty($msgServiceAdress->county)
                ->setPostcode($msgServiceAdress->postcode)
                ->setCountry($msgServiceAdress->country);
        }

        $residentialAddress = new Address;
        if (!array_key_exists('residentialAddress', $data) || $data['residentialAddress']) {
            $residentialAddress
                ->setPremise($data['residential_premise'])
                ->setStreet($data['residential_street'])
                ->setThoroughfare($data['residential_thoroughfare'])
                ->setPostTown($data['residential_post_town'])
                ->setCounty($data['residential_county'])
                ->setPostcode($data['residential_postcode'])
                ->setCountry($data['residential_country']);
        } else {
            $residentialAddress
                ->setPremise($data['premise'])
                ->setStreet($data['street'])
                ->setThoroughfare($data['thoroughfare'])
                ->setPostTown($data['post_town'])
                ->setCounty($data['county'])
                ->setPostcode($data['postcode'])
                ->setCountry($data['country']);
        }

        $personPscData = new PersonPscData();
        $personPscData
            ->setTitle($data['title'])
            ->setFirstName($data['forename'])
            ->setMiddleName($data['middle_name'])
            ->setLastName($data['surname'])
            ->setNationality($data['nationality'])
            ->setDateOfBirth(isset($data['dob']) ? new DateTime($data['dob']) : NULL)
            ->setCountryOfResidence($data['country_of_residence'])
            ->setOwnershipOfShares($data['ownership_of_shares'] ?? NULL)
            ->setOwnershipOfVotingRights($data['ownership_of_voting_rights'])
            ->setRightToAppointAndRemoveDirectors($data['right_to_appoint_and_remove_directors'])
            ->setSignificantInfluenceOrControl($data['significant_influence_or_control'])
            ->setResidentialAddress($residentialAddress)
            ->setServiceAddress($serviceAddress)
            ->setNotificationDate(isset($data['notification_date']) ? new DateTime($data['notification_date']) : NULL)
            ->setDateOfChange(isset($data['date_of_change']) ? new DateTime($data['date_of_change']) : NULL)
            ->setChangePerson(isset($data['changePerson']) && $data['changePerson'] == 1)
            ->setChangeServiceAddress(isset($data['changeServiceAddress']) && $data['changeServiceAddress'] == 1)
            ->setChangeResidentialAddress(isset($data['changeResidentialAddress']) && $data['changeResidentialAddress'] == 1)
            ->setChangeNatureOfControl(isset($data['changeNatureOfControl']) && $data['changeNatureOfControl'] == 1);

        $cb = $this->onValidCallback;
        $cb($personPscData, $form);
    }

    /**
     * @param array $fieldValues
     * @param string $name
     * @return CHForm
     */
    private function buildForm(array $fieldValues, $name)
    {
        $companyType = $this->company->getCompanyCategory();
        $form = new CHForm('pscPerson' . $this->company->getId() . $name);
        $isEmpty = empty($fieldValues['forename']);

        // prefill
        if (!$this->formOptions->isPscChange()) {
            $form->addFieldset('Prefill');
            $form->addSelect('prefillOfficers', 'Select an existing appointment to autocomplete these details', $this->prefillOfficers['select'])
                ->setFirstOption('--- Select --')
                ->class($this->isAdminView ? '' : 'form-control');
        }

        // person
        $form->addFieldset('Person');

        if ($this->formOptions->isPscChange()) {
            $form->addCheckbox('changePerson', 'change', 1)
                ->addAtrib('rv-changedetails', '')
                ->addAtrib('class', 'changeToggler');
        }

        $form->addSelect('title', 'Title *', Person::$titles)
            ->setFirstOption('--- Select ---')
            ->addRule(FForm::Required, 'Please provide title')
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('forename', 'First name *')
            ->addRule([$this, 'Validator_requiredField'], 'Please provide first name', 'changePerson')
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->addRule('MaxLength', "First name can't be more than 50 characters", 50)
            ->addAtrib('rv-value', 'idDataChecker.personalData.forename')
            ->addAtrib('rv-on-blur', 'checkIdData')
            ->addAtrib('class', $this->isAdminView ? '' : 'form-control');
        $form->addText('middle_name', 'Middle name')
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->addRule('MaxLength', "Middle name can't be more than 50 characters", 50)
            ->addRule('MinLength', "Please provide full middle name.", 2)
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('surname', 'Last name *')
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->addRule([$this, 'Validator_requiredField'], 'Please provide last name', 'changePerson')
            ->addRule('MaxLength', "Last name can't be more than 160 characters", 160)
            ->addAtrib('rv-value', 'idDataChecker.personalData.surname')
            ->addAtrib('rv-on-blur', 'checkIdData')
            ->addAtrib('class', $this->isAdminView ? '' : 'form-control');

        if (!$this->formOptions->isPscChange()) {
            $form->addText('dob', 'Date Of Birth *')
                ->addRule(FForm::Required, 'Please provide date of birth')
                ->addRule(
                    [$this, 'Validator_personDOB'],
                    ['DOB is not a valid date.', 'PSC has to be older than %d years.'],
                    16
                )
                ->addAtrib('class', 'paulo-ui-datepicker ' . ($this->isAdminView ? '' : 'form-control form-control-date'))
                ->addAtrib('data-date-format', 'yy-mm-dd')
                ->addAtrib('rv-value', 'idDataChecker.personalData.dob')
                ->addAtrib('rv-on-input', 'checkIdData');
        }

        $form->addSelect('nationality', 'Nationality *', $this->nationalities)
            ->setFirstOption('--- Select an option ---')
            ->class($this->isAdminView ? 'searchable-select' : 'searchable-select form-control')
            ->addRule(FForm::Required, 'Please provide Country of Residence');

        $form->addSelect(
            'country_of_residence',
            'Country Of Residence *',
            $this->chCountriesRepository->getPersonCountriesOfResidence()
        )
            ->class($this->isAdminView ? '' : 'form-control')
            ->setFirstOption('--- Select a country ---')
            ->addAtrib('rv-on-change', 'countryOfResidenceUpdated')
            ->addAtrib('rv-value', "personAddress.countryOfResidence.value")
            ->addRule(FForm::Required, 'Please provide Country of Residence');

        if ($this->company->canUseOurServiceAddress()) {
            $form->addFieldset('Use Our Service Address service');
            $checkbox = $form
                ->addCheckbox('ourServiceAddress', 'Service Address service  ', 1)
                ->addAtrib('rv-checked',"serviceAddress.isMsgAddress")
                ->addAtrib('rv-on-change', 'serviceAddressChanged');

            if ($this->data->getServiceAddress()) {
                $msgServiceAdress = $this->ourServiceAddressProvider->getProduct();
                if ($this->data->getServiceAddress()->getPostcode() == $msgServiceAdress->postcode) {
                    $checkbox->setValue(1);
                }
            }
        }

        $form->addFieldset('Service Address Prefill');

        if ($this->formOptions->isPscChange()) {
            $form->addCheckbox('changeServiceAddress', 'change', 1)
                ->addAtrib('rv-changedetails', '')
                ->addAtrib('class', 'changeToggler');
        }

        if (!$this->formOptions->isPscChange()) {

            if (!$this->isAdminView) {
            $form->addCheckbox('entityChangeEnabled', 'Enable update of address information', '1')
                ->addAtrib('rv-checked', 'idDataChecker.updateEnabled');
            }

            $form->addSelect('prefillAddress', 'Prefill Address', $this->prefillAddresses['select'])
                ->setFirstOption('--- Select ---')
                ->class($this->isAdminView ? '' : 'form-control');

        }

        $form->addFieldset('Address');
        $form->addText('premise', 'Building name/number *')
            ->addRule([$this, 'Validator_requiredAddress'], 'Please provide Building name/number')
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->addRule('MaxLength', "Building name/number can't be more than 50 characters", 50)
            ->addAtrib('rv-value', 'idDataChecker.addressData.premise')
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('street', 'Street *')
            ->addRule([$this, 'Validator_requiredAddress'], 'Please provide Street')
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->addRule('MaxLength', "Street can't be more than 50 characters", 50)
            ->addAtrib('rv-value', 'idDataChecker.addressData.street')
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('thoroughfare', 'Address 3')
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->addRule('MaxLength', "Address 3 can't be more than 50 characters", 50)
            ->addAtrib('rv-value', 'idDataChecker.addressData.thoroughfare')
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('post_town', 'Town *')
            ->addRule([$this, 'Validator_requiredAddress'], 'Please provide Town')
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->addRule('MaxLength', "Town can't be more than 50 characters", 50)
            ->addAtrib('rv-value', 'idDataChecker.addressData.city')
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('county', 'County')
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->addRule('MaxLength', "County can't be more than 50 characters", 50)
            ->addAtrib('rv-value', 'idDataChecker.addressData.county')
            ->class($this->isAdminView ? '' : 'form-control');

        $postcode = $form->addText('postcode', 'Postcode *')
            ->addRule([$this, 'Validator_requiredAddress'], 'Please provide Postcode')
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->addRule('MaxLength', "Postcode can't be more than 15 characters", 15)
            ->addAtrib('rv-value', 'idDataChecker.addressData.postcode')
            ->class($this->isAdminView ? '' : 'form-control');
        if (!$this->isAdminView) {
            $postcode->addRule(
                [CHValidator::class, 'Validator_serviceAddressPostCode'],
                sprintf(
                    'You cannot use our postcode for this address without first purchasing the <a href="%s">Registered Office Service</a>.',
                    $this->router->generate('product_module.registered_office')
                ),
                $this->company
            );
        }

        $form->addSelect('country', 'Country *', $this->chCountriesRepository->getServiceAddressCountries())
            ->setFirstOption('--- Select a country --')
            ->addRule([$this, 'Validator_requiredAddress'], 'Please provide Country')
            ->addAtrib('rv-value', 'idDataChecker.addressData.country')
            ->addAtrib('rv-on-change', 'serviceAddressCountryChanged')
            ->class($this->isAdminView ? '' : 'form-control');

        if ($this->isAdminView) {
            $form->addFieldset('Residential Address Prefill');
            $form->addSelect('prefillResidentialAddress', 'Prefill Address', $this->prefillAddresses['select'])
                ->setFirstOption('--- Select ---')
                ->class($this->isAdminView ? '' : 'form-control');
        }

        $form->addFieldset('Residential Address');

        if ($this->formOptions->isPscChange()) {
            $form->addCheckbox('changeResidentialAddress', 'change', 1)
                ->addAtrib('rv-changedetails', '')
                ->addAtrib('class', 'changeToggler');
        }

        if ($isEmpty) {
            $form->addCheckbox('residentialAddress', 'Different address', 1)
                ->addAtrib('rv-checked', 'residentialAddress.different');
        }

        $form->addText('residential_premise', 'Building name/number *')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addAtrib('data-pca-form', 'premise')
            ->addRule([$this, 'Validator_requiredServiceAddress'], 'Please provide Building name/number', $isEmpty)
            ->addRule('MaxLength', "Building name/number can't be more than 50 characters", 50)
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('residential_street', 'Street *')
            ->addAtrib('data-pca-form', 'street')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addRule([$this, 'Validator_requiredServiceAddress'], 'Please provide street', $isEmpty)
            ->addRule('MaxLength', "Street can't be more than 50 characters", 50)
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('residential_thoroughfare', 'Address 3')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addAtrib('data-pca-form', 'thoroughfare')
            ->addRule('MaxLength', "Address 3 can't be more than 50 characters", 50)
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('residential_post_town', 'Town *')
            ->addAtrib('data-pca-form', 'city')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addRule([$this, 'Validator_requiredServiceAddress'], 'Please provide Town', $isEmpty)
            ->addRule('MaxLength', "Town can't be more than 50 characters", 50)
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('residential_county', 'County')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addRule('MaxLength', "County can't be more than 50 characters", 50)
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->class($this->isAdminView ? '' : 'form-control');
        $form->addText('residential_postcode', 'Postcode *')
            ->addAtrib('data-pca-form', 'postcode')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addRule([$this, 'Validator_requiredServiceAddress'], 'Please provide Postcode', $isEmpty)
            ->addRule('MaxLength', "Postcode can't be more than 15 characters", 15)
            ->addRule([EnglishKeyboardValidator::class, 'isValid'], NULL)
            ->class($this->isAdminView ? '' : 'form-control')
            ->addRule(
                FForm::REGEXP,
                'You cannot use our postcode for residential address',
                ['#(ec1v 4pw|ec1v4pw)#i', TRUE]
            );
        $form->addSelect('residential_country', 'Country *', $this->chCountriesRepository->getResidentialAddressCountries())
            ->setFirstOption('--- Select a country --')
            ->addAtrib('rv-hide', "residentialAddress.isCountryHidden")
            ->addAtrib('rv-value', "residentialAddress.country.value")
            ->addRule([$this, 'Validator_requiredServiceAddress'], 'Please provide Country', $isEmpty)
            ->class($this->isAdminView ? '' : 'form-control');

        $form->addFieldset('Nature of Control:');

        if ($this->formOptions->isPscChange()) {
            $form->addCheckbox('changeNatureOfControl', 'change', 1)
                ->addAtrib('rv-changedetails', '')
                ->addAtrib('class', 'changeToggler');
        }

        if ($companyType != CompanyIncorporation::BY_GUARANTEE) {
            $form->addRadio(
                'ownership_of_shares',
                'The person holds shares',
                $this->pscChoicesProvider->getAllOwnershipOfShares($companyType)
            )
                ->addAtrib('rv-on-click', 'checkOptions');
        }

        $form->addRadio(
            'ownership_of_voting_rights',
            'The person holds voting rights',
            $this->pscChoicesProvider->getAllOwnershipOfVotingRights()
        )
            ->addAtrib('rv-on-click', 'checkOptions');

        $form->addRadio(
            'right_to_appoint_and_remove_directors',
            'Right to appoint or remove the majority of the board of directors',
            $this->pscChoicesProvider->getRightToAppointAndRemoveDirectors(PscChoicesProvider::PERSON, $companyType)
        )
            ->addAtrib('rv-on-click', 'checkOptions');

        $form
            ->addRadio(
                'significant_influence_or_control',
                'Has significant influence or control',
                $this->pscChoicesProvider->getSignificantInfluenceOrControl(PscChoicesProvider::PERSON)
            )
            ->addAtrib('rv-on-click', 'checkOptions')
            ->addRule(
                [$this, 'Validator_natureOfControl'],
                ['Sorry, you cannot select "has significant influence or control" when "ownership of shares", "ownership of voting rights" or "right to appoint or remove directors" is also selected.', 'Please select the nature of control. At least one condition must be selected.'],
                $companyType
            );

        if ($this->formOptions->isNotification()) {
            $form->add(DatePickerNew::class, 'notification_date', 'Notification Date:')
                ->addRule(FForm::Required, 'Please provide date.')
                ->addRule([CHValidator::class, 'Validator_pscDate'])
                ->addRule(
                    [CHValidator::class, 'Validator_cannotBeInFuture'],
                    'Sorry, you cannot set your notification date to after the made up to date of the Confirmation Statement. Please set a prior date.',
                    $this->formOptions->getMadeUpToDate()
                )
                ->class('date');
        } elseif ($this->formOptions->isChange()) {
            $form->add(DatePickerNew::class, 'date_of_change', 'Date of change:')
                ->addRule(FForm::Required, 'Please provide date.')
                ->addRule([CHValidator::class, 'Validator_pscDate'])
                ->addRule(
                    [CHValidator::class, 'Validator_cannotBeInFuture'],
                    'Sorry, you cannot set your date of change to after the made up to date of the Confirmation Statement. Please set a prior date.',
                    $this->formOptions->getMadeUpToDate()
                )
                ->addRule(
                    [CHValidator::class, 'Validator_cannotBeInPast'],
                    'Sorry, you cannot set your date of change to before the notification date of the PSC. Please set a later date.',
                    $this->notificationDateValue
                )
                ->class('date');
        } elseif ($this->formOptions->isPscNotification()) {
            $form->addFieldset('Notification date:');

            $form->add(DatePickerNew::class, 'notification_date', 'The date they became a PSC:')
                ->addRule(FForm::Required, 'Please provide date.')
                ->addRule([CHValidator::class, 'Validator_pscDate'], 'Sorry, this cannot be before 6 April 2016 as this was when PSCs were introduced.')
                ->addRule(
                    [CHValidator::class, 'Validator_cannotBeInFuture'],
                    'Sorry, this date cannot be in the future.',
                    new DateTime()
                )
                ->class($this->isAdminView ? 'date' : 'date form-control');
        } elseif ($this->formOptions->isPscChange()) {
            $form->add(DatePickerNew::class, 'date_of_change', 'When did the details of the PSC change?')
                ->addRule(FForm::Required, 'Please provide date.')
                ->addRule([CHValidator::class, 'Validator_pscDate'], 'Sorry, this cannot be before 6 April 2016 as this was when PSCs were introduced.')
                ->addRule(
                    [CHValidator::class, 'Validator_cannotBeInFuture'],
                    'Sorry, this date cannot be in the future.',
                    new DateTime()
                )
                ->class('date form-control');
        }

        if ($name === 'add') {
            $form->setStorable(FALSE);
        } else {
            $form->setInitValues($fieldValues);
        }

        $form->onValid = [$this, 'onValid'];

        $form->start();
        return $form;
    }


    public function Validator_requiredField(FControl $control, string $error, $changeControl)
    {
        $value = $control->getValue();

        if ($this->formOptions->isPscChange()) {
            if ($control->owner[$changeControl]->getValue() == 1 && empty($value)) {
                return $error;
            }

        } else if (empty($value)) {

            return $error;
        }

        return TRUE;
    }

    /**
     * @param Radio $control
     * @param string $error
     * @param string $companyType
     * @return bool
     */
    public function Validator_natureOfControl(Radio $control, $error, $companyType)
    {
        /** @var CHForm $form */
        $form = $control->owner;

        $isSignificantInfluenceOfControlAllowed = $this->isSignificantInfluenceOfControlAllowed($form, $companyType);

        if($this->formOptions->isPscChange() && $form['changeNatureOfControl']->getValue() != 1){
            return TRUE;
        }

        if ($form['significant_influence_or_control']->getValue() != NULL) {

            if (!$isSignificantInfluenceOfControlAllowed) {
                return $error[0];
            }

        } else {

            if (!$this->isAnyOptionFromNatureOfControlChosen($form, $companyType)) {
                return $error[1];
            }

        }

        return TRUE;
    }

    /**
     * @param DateSelect $control
     * @param array $error
     * @param int $minYears
     * @return bool|string
     */
    public function Validator_personDOB($control, $error, $yearsLimit)
    {
        $value = $control->getValue();

        // check if dob is valid day
        $temp = explode('-', $value);

        if (!isset($temp[0], $temp[1], $temp[2])) {
            return $error[0];
        }

        if (checkdate($temp[1], $temp[2], $temp[0]) === FALSE) {
            return $error[0];
        }

        if (is_array($yearsLimit) && isset($yearsLimit[0], $yearsLimit[1])) {
            $minYears = $yearsLimit[0];
            $maxYears = $yearsLimit[1];
        } else {
            if (is_int($yearsLimit)) {
                $minYears = $yearsLimit;
            } else {
                $minYears = 0;
            }
            $maxYears = NULL;
        }

        // check if director is older than $minYears years
        $dob = date('Ymd', strtotime($value));
        $years = floor((date("Ymd") - $dob) / 10000);
        if ($years < $minYears) {
            return sprintf($error[1], $minYears);
        }
        // check if director is younger than $maxYears years
        if ($maxYears && $years >= $maxYears) {
            return sprintf($error[2], $maxYears);
        }
        return TRUE;
    }

    /**
     * @param FControl $control
     * @param string $error
     * @return bool|string
     */
    public function Validator_requiredAddress(FControl $control, $error)
    {
        $value = $control->getValue();

        if($this->formOptions->isPscChange() && $control->owner['changeServiceAddress']->getValue() != 1){
            return TRUE;
        }

        //service address service validator
        if (isset($control->owner['ourServiceAddress'])) {
            if ($control->owner['ourServiceAddress']->getValue() != 1 && empty($value)) {
                return $error;
            }
            //without service address
        } else if (empty($value)) {
            return $error;
        }
        return TRUE;
    }

    public function Validator_cannotBeInFuture(FControl $control, $error)
    {
        if (strtotime($control->getValue()) < strtotime(date('2016-04-07 00:00:00'))) {
            return $error ?: 'The PSCs were introduced on 6 April 2016, therefore your date cannot be before this date.';
        }
    }

    /**
     * @param $control
     * @param $error
     * @param $isEmpty
     * @return bool
     */
    public function Validator_requiredServiceAddress($control, $error, $isEmpty)
    {
        $value = $control->getValue();

        $postcodeValue = $control->owner['postcode']->getValue();
        $isMsgAddress = false;
        if (is_string($postcodeValue)) {
            $normalizedPostcode = str_replace(' ', '', strtoupper($postcodeValue));
            $isMsgAddress = strcmp($normalizedPostcode, 'N17GU') === 0;
        }
        $residentialMustBeFilled = empty($value) || $isMsgAddress;

        if($this->formOptions->isPscChange() && $control->owner['changeResidentialAddress']->getValue() != 1){
            return TRUE;
        }

        if ($isEmpty) {
            if ($control->owner['residentialAddress']->getValue() == 1 && $residentialMustBeFilled) {
                return $error;
            }
        } else if (empty($value)) {

            return $error;
        }

        return TRUE;
    }

    private function isSignificantInfluenceOfControlAllowed(CHForm $form, string $companyType): bool
    {
        if ($companyType ===  CompanyIncorporation::BY_GUARANTEE) {
            return is_null($form['ownership_of_voting_rights']->getValue())
                && is_null($form['right_to_appoint_and_remove_directors']->getValue());
        }

        return is_null($form['ownership_of_shares']->getValue())
            && is_null($form['ownership_of_voting_rights']->getValue())
            && is_null($form['right_to_appoint_and_remove_directors']->getValue());
    }


    /**
     * @param array $form
     * @param string $companyType
     * @return bool
     */
    private function isAnyOptionFromNatureOfControlChosen($form, $companyType)
    {
        $toCheck = [
            $form['ownership_of_voting_rights']->getValue(),
            $form['right_to_appoint_and_remove_directors']->getValue(),
            $form['significant_influence_or_control']->getValue(),
        ];

        if ($companyType != CompanyIncorporation::BY_GUARANTEE) {
            $toCheck[] = $form['ownership_of_shares']->getValue();
        }

        if (empty(array_filter($toCheck))) {
            return FALSE;
        }

        return TRUE;
    }

    /**
     * @param $companyType
     * @return array
     */
    private function getNatureOfControlOptions($companyType)
    {

        $options = $this->pscChoicesProvider->getOwnershipOfVotingRights() +
            $this->pscChoicesProvider->getOwnershipOfShares($companyType);

        $options['RIGHTTOAPPOINTANDREMOVEDIRECTORS'] = TRUE;

        return $options;
    }

}
