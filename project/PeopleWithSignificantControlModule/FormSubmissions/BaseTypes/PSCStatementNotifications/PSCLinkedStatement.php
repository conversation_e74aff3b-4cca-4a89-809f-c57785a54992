<?php

namespace PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\PSCStatementNotifications;

use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\IPerson;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\PSCStatementNotifications\PersonTypes\Corporate;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\PSCStatementNotifications\PersonTypes\Individual;
use PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\PSCStatementNotifications\PersonTypes\LegalPerson;

class PSCLinkedStatement
{
    /**
     * @var string
     */
    private $statement;

    /**
     * @var IPerson
     */
    private $person;

    /**
     * @param string $statement
     * @param IPerson $person
     */
    public function __construct(string $statement, IPerson $person)
    {
        $this->statement = $statement;
        $this->person = $person;
    }

    /**
     * @return string
     */
    public function getStatement(): string
    {
        return $this->statement;
    }

    /**
     * @return Individual|IPerson|null
     */
    public function getIndividual(): ?Individual
    {
        return $this->getVirtualProperty(Individual::class);
    }

    /**
     * @return Corporate|IPerson|null
     */
    public function getCorporate(): ?Corporate
    {
        return $this->getVirtualProperty(Corporate::class);
    }

    /**
     * @return LegalPerson|IPerson|null
     */
    public function getLegalPerson(): ?LegalPerson
    {
        return $this->getVirtualProperty(LegalPerson::class);
    }

    /**
     * @param string $className
     * @return null|IPerson
     */
    private function getVirtualProperty(string $className): ?IPerson
    {
        return $this->person instanceof $className ? $this->person : NULL;
    }
}
