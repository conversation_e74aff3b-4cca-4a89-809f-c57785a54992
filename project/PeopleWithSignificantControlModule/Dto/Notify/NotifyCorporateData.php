<?php

namespace PeopleWithSignificantControlModule\Dto\Notify;

use CompaniesHouseModule\Dto\CorporateData;

class NotifyCorporateData
{
    /**
     * @var CorporateData
     */
    private $corporate;

    public static function default(): NotifyCorporateData
    {
        $self = new self();
        $self->setCorporate(CorporateData::notify());
        return $self;
    }

    /**
     * @return CorporateData
     */
    public function getCorporate(): CorporateData
    {
        return $this->corporate;
    }

    /**
     * @param CorporateData $corporate
     */
    public function setCorporate(CorporateData $corporate)
    {
        $this->corporate = $corporate;
    }
}