<?php

namespace MailScanModule\Facades;

use CompanyModule\Domain\Company\CompanyName;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\OptimisticLockException;
use Entities\Customer;
use iio\libmergepdf\Exception;
use MailScanModule\Entities\PostItem;

class PostItemInjectionFacade
{
    /**
     * @var EntityManager
     */
    private $entityManager;

    public function __construct(EntityManager $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * @throws Exception
     * @throws DBALException
     * @throws OptimisticLockException
     */
    public function injectPostItems(Customer $customer): void
    {
        $companies = $customer->getCompanies();

        if (empty($companies)) {
            throw new Exception('Customer has no companies');
        }

        foreach ($companies as $company) {
            $companyNumber = $company->getCompanyNumber();

            if (!$company->isIncorporated()) {
                continue;
            }

            $voData[$companyNumber] = 'LP' . random_int(10000, 90000);

            $postItems[] = new PostItem(
                PostItem::TYPE_COMPANIES_HOUSE,
                PostItem::STATUS_WAITING,
                CompanyName::uppercased($company->getCompanyName()),
                $companyNumber,
                $voData[$companyNumber] ?? null
            );
            $postItems[] = new PostItem(
                PostItem::TYPE_HMRC,
                PostItem::STATUS_WAITING,
                CompanyName::uppercased($company->getCompanyName()),
                $companyNumber,
                $voData[$companyNumber] ?? null
            );
        }

        foreach ($postItems as $entity) {
            $this->entityManager->persist($entity);
        }
        $this->entityManager->flush();
    }
}
