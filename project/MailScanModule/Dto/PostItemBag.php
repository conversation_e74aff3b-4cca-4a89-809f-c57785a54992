<?php

declare(strict_types=1);

namespace MailScanModule\Dto;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Entities\Customer;

readonly class PostItemBag implements \JsonSerializable
{
    public function __construct(
        private ArrayCollection $postItems,
    ) {
    }

    public function isEmpty(): bool
    {
        return $this->postItems->isEmpty();
    }

    public function getAllPostItems(): array
    {
        return $this->postItems->toArray();
    }

    public function getCompaniesInBag(): array
    {
        $companies = [];
        /** @var MailroomPostItemData $postItem */
        foreach ($this->postItems as $postItem) {
            if (!in_array($postItem->getCompanyNumber(), $companies)) {
                $companies[] = $postItem->getCompanyNumber();
            }
        }

        return $companies;
    }

    public function getCompanyPostItems(string $companyNumber): array
    {
        $companyPostItems = [];
        foreach ($this->postItems as $postItem) {
            if ($postItem->getCompanyNumber() === $companyNumber) {
                $companyPostItems[] = $postItem;
            }
        }

        return $companyPostItems;
    }

    public function getCustomerPostItems(Customer $customer): array
    {
        $customerCompanies = $customer->getCompanies();
        $customerCompanyNumbers = [];
        foreach ($customerCompanies as $company) {
            $customerCompanyNumbers[] = $company->getCompanyNumber();
        }

        $customerPostItems = [];
        foreach ($this->postItems as $postItem) {
            if (in_array($postItem->getCompanyNumber(), $customerCompanyNumbers)) {
                $customerPostItems[] = $postItem;
            }
        }

        return $customerPostItems;
    }

    public function countAllPostItems(): ?int
    {
        return $this->postItems->count();
    }

    public function getLastItemId(): ?string
    {
        if ($this->postItems->isEmpty()) {
            return null;
        }

        $lastItem = $this->postItems->last();
        return $lastItem ? (string) $lastItem->getId() : null;
    }

    public function mergeWith(PostItemBag $otherBag): PostItemBag
    {
        $mergedItems = new ArrayCollection(array_merge(
            $this->postItems->toArray(),
            $otherBag->getAllPostItems()
        ));

        return new PostItemBag($mergedItems);
    }

    public function jsonSerialize(): array
    {
        return $this->postItems->toArray();
    }
}
