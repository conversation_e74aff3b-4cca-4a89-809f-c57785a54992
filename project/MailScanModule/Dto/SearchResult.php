<?php

namespace MailScanModule\Dto;

class SearchResult implements \JsonSerializable
{
    /**
     * @var SearchResultRow[]
     */
    private array $rows;

    public function __construct(array $rows)
    {
        $this->rows = $rows;
    }

    public static function from(array $row): self
    {
        return new self($row);
    }

    public static function empty(): self
    {
        return self::from([]);
    }

    public function union(SearchResult $that): self
    {
        return self::from(
            array_merge([], $this->getRows(), $that->getRows())
        );
    }

    public function getRows(): array
    {
        return $this->rows;
    }

    public function truncate(int $limit): array
    {
        return array_slice($this->rows, 0, $limit);
    }

    public function getCompanies(): iterable
    {
        foreach ($this->rows as $row) {
            if ($row->isCompanyType()) {
                yield $row;
            }
        }
    }

    public function getPeople(): iterable
    {
        foreach ($this->rows as $row) {
            if ($row->isPersonType()) {
                yield $row;
            }
        }
    }

    public function getCompanyIds(): array
    {
        return array_unique(
            array_map(
                function (SearchResultRow $row) {
                    return $row->getId();
                },
                $this->rows
            )
        );
    }

    public function filterByIds(array $ids): self
    {
        return self::from(
            array_values(
                array_filter(
                    $this->rows,
                    function (SearchResultRow $row) use ($ids) {
                        return in_array($row->getId(), $ids);
                    }
                )
            )
        );
    }

    public function mapServices(CompanyServicesCollection $services): self
    {
        $data = array_filter(
            array_map(
                function (SearchResultRow $row) use ($services) {
                    $companyServices = $services->getByCompanyId($row->getId());
                    if ($companyServices->isEmpty()) {
                        return;
                    }

                    return $row->addServices($companyServices);
                },
                $this->rows
            ), function ($row) {
                return !empty($row);
            });

        return self::from($data);
    }

    public function sortByService(): self
    {
        $rows = array_slice($this->rows, 0);

        usort(
            $rows,
            function (SearchResultRow $r1, SearchResultRow $r2) {
                if ($r1->getServicesCollection()->isEmpty() === $r2->getServicesCollection()->isEmpty()) {
                    return 0;
                }

                if ($r1->getServicesCollection()->isEmpty()) {
                    return 1;
                }

                return -1;
            }
        );

        return self::from($rows);
    }

    public function sortByServiceStatus(): self
    {
        $rows = array_slice($this->rows, 0);

        usort(
            $rows,
            function (SearchResultRow $r1, SearchResultRow $r2) {
                if ($r1->getServicesCollection()->hasActiveService() === $r2->getServicesCollection()->hasActiveService()) {
                    return 0;
                }

                return $r2->getServicesCollection()->hasActiveService() ? 1 : -1;
            }
        );

        return self::from($rows);
    }

    public function isEmpty(): bool
    {
        return empty($this->rows);
    }

    public function jsonSerialize(): array
    {
        return $this->rows;
    }
}
